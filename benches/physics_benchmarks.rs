use criterion::{black_box, criterion_group, criterion_main, Criterion, BenchmarkId};
use nalgebra::Vector2;
use verturion::physics::PhysicsWorld;

/// Benchmark physics world creation
fn bench_world_creation(c: &mut Criterion) {
    c.bench_function("physics_world_creation", |b| {
        b.iter(|| {
            black_box(PhysicsWorld::new())
        })
    });
}

/// Benchmark single physics step with varying object counts
fn bench_physics_step(c: &mut Criterion) {
    let mut group = c.benchmark_group("physics_step");
    
    for object_count in [1, 10, 50, 100, 200].iter() {
        group.bench_with_input(
            BenchmarkId::new("objects", object_count),
            object_count,
            |b, &object_count| {
                let mut world = PhysicsWorld::new();
                
                // Add objects to the world
                for i in 0..object_count {
                    let x = (i % 10) as f32 * 2.0 - 10.0;
                    let y = (i / 10) as f32 * 2.0 + 5.0;
                    world.create_ball(Vector2::new(x, y), 0.5);
                }
                
                b.iter(|| {
                    world.step();
                })
            },
        );
    }
    group.finish();
}

/// Benchmark object creation performance
fn bench_object_creation(c: &mut Criterion) {
    let mut group = c.benchmark_group("object_creation");
    
    group.bench_function("ball_creation", |b| {
        let mut world = PhysicsWorld::new();
        let mut counter = 0;
        
        b.iter(|| {
            let position = Vector2::new(
                (counter % 10) as f32 * 0.1,
                (counter / 10) as f32 * 0.1 + 5.0,
            );
            counter += 1;
            black_box(world.create_ball(position, 0.5))
        })
    });
    
    group.bench_function("box_creation", |b| {
        let mut world = PhysicsWorld::new();
        let mut counter = 0;
        
        b.iter(|| {
            let position = Vector2::new(
                (counter % 10) as f32 * 0.1,
                (counter / 10) as f32 * 0.1 + 5.0,
            );
            counter += 1;
            black_box(world.create_box(position, Vector2::new(0.5, 0.5)))
        })
    });
    
    group.finish();
}

/// Benchmark collision detection with many objects
fn bench_collision_detection(c: &mut Criterion) {
    let mut group = c.benchmark_group("collision_detection");
    
    for density in [0.5, 1.0, 2.0].iter() {
        group.bench_with_input(
            BenchmarkId::new("density", density),
            density,
            |b, &density| {
                let mut world = PhysicsWorld::new();
                
                // Create a dense grid of objects
                let grid_size = (20.0 * density) as i32;
                for x in -grid_size..grid_size {
                    for y in 0..grid_size {
                        let position = Vector2::new(
                            x as f32 * (1.0 / density),
                            y as f32 * (1.0 / density) + 5.0,
                        );
                        world.create_ball(position, 0.4 / density);
                    }
                }
                
                b.iter(|| {
                    world.step();
                })
            },
        );
    }
    group.finish();
}

/// Benchmark impulse application performance
fn bench_impulse_application(c: &mut Criterion) {
    let mut world = PhysicsWorld::new();
    let mut handles = Vec::new();
    
    // Create objects to apply impulses to
    for i in 0..100 {
        let x = (i % 10) as f32 * 2.0 - 10.0;
        let y = (i / 10) as f32 * 2.0 + 5.0;
        let handle = world.create_ball(Vector2::new(x, y), 0.5);
        handles.push(handle);
    }
    
    c.bench_function("impulse_application", |b| {
        let mut counter = 0;
        b.iter(|| {
            let handle = handles[counter % handles.len()];
            let impulse = Vector2::new(
                ((counter as f32 * 0.1).sin() * 10.0),
                ((counter as f32 * 0.1).cos() * 10.0),
            );
            counter += 1;
            world.apply_impulse(handle, impulse);
        })
    });
}

/// Benchmark object removal performance
fn bench_object_removal(c: &mut Criterion) {
    c.bench_function("object_removal", |b| {
        b.iter_batched(
            || {
                let mut world = PhysicsWorld::new();
                let mut handles = Vec::new();
                
                // Create objects to remove
                for i in 0..50 {
                    let x = (i % 10) as f32 * 2.0 - 10.0;
                    let y = (i / 10) as f32 * 2.0 + 5.0;
                    let handle = world.create_ball(Vector2::new(x, y), 0.5);
                    handles.push(handle);
                }
                
                (world, handles)
            },
            |(mut world, handles)| {
                for handle in handles {
                    world.remove_object(handle);
                }
                black_box(world)
            },
            criterion::BatchSize::SmallInput,
        )
    });
}

/// Benchmark gravity changes
fn bench_gravity_changes(c: &mut Criterion) {
    let mut world = PhysicsWorld::new();
    
    // Add some objects
    for i in 0..20 {
        let x = (i % 5) as f32 * 2.0 - 5.0;
        let y = (i / 5) as f32 * 2.0 + 5.0;
        world.create_ball(Vector2::new(x, y), 0.5);
    }
    
    c.bench_function("gravity_changes", |b| {
        let mut counter = 0;
        b.iter(|| {
            let gravity = Vector2::new(
                ((counter as f32 * 0.1).sin() * 5.0),
                -9.81 + ((counter as f32 * 0.1).cos() * 2.0),
            );
            counter += 1;
            world.set_gravity(gravity);
            world.step();
        })
    });
}

/// Benchmark stress test with many objects and long simulation
fn bench_stress_test(c: &mut Criterion) {
    let mut group = c.benchmark_group("stress_test");
    group.sample_size(10); // Reduce sample size for stress tests
    
    group.bench_function("many_objects_simulation", |b| {
        b.iter_batched(
            || {
                let mut world = PhysicsWorld::new();
                
                // Create many objects in a tower
                for layer in 0..10 {
                    for i in 0..10 {
                        let x = (i as f32 - 5.0) * 1.1;
                        let y = layer as f32 * 2.2 + 5.0;
                        world.create_box(Vector2::new(x, y), Vector2::new(0.5, 0.5));
                    }
                }
                
                world
            },
            |mut world| {
                // Simulate for many steps
                for _ in 0..100 {
                    world.step();
                }
                black_box(world)
            },
            criterion::BatchSize::SmallInput,
        )
    });
    
    group.finish();
}

/// Benchmark memory allocation patterns
fn bench_memory_patterns(c: &mut Criterion) {
    let mut group = c.benchmark_group("memory_patterns");
    
    group.bench_function("create_destroy_cycle", |b| {
        let mut world = PhysicsWorld::new();
        
        b.iter(|| {
            let mut handles = Vec::new();
            
            // Create objects
            for i in 0..20 {
                let x = (i % 5) as f32 * 2.0 - 5.0;
                let y = (i / 5) as f32 * 2.0 + 5.0;
                let handle = world.create_ball(Vector2::new(x, y), 0.5);
                handles.push(handle);
            }
            
            // Step simulation
            for _ in 0..10 {
                world.step();
            }
            
            // Remove objects
            for handle in handles {
                world.remove_object(handle);
            }
            
            black_box(&world)
        })
    });
    
    group.finish();
}

criterion_group!(
    benches,
    bench_world_creation,
    bench_physics_step,
    bench_object_creation,
    bench_collision_detection,
    bench_impulse_application,
    bench_object_removal,
    bench_gravity_changes,
    bench_stress_test,
    bench_memory_patterns
);

criterion_main!(benches);
