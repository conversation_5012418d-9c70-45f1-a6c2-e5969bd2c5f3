# Verturion - Interactive 2D Physics Simulation

A high-performance, interactive 2D physics simulation built with Rust, featuring real-time visualization, comprehensive benchmarking, and an intuitive debug interface.

## Features

### 🎮 Interactive Physics Simulation
- **Real-time 2D physics** powered by Rapier2D
- **Interactive object spawning** via mouse clicks
- **Multiple object types**: Balls, boxes, and ground surfaces
- **Configurable physics parameters**: Gravity, object size, material properties

### 🖱️ Mouse & Keyboard Controls
- **Left Click**: Spawn objects at cursor position
- **F1**: Toggle help panel
- **ESC**: Exit application
- **SPACE**: Pause/Resume physics simulation
- **R**: Clear all dynamic objects
- **G**: Toggle gravity on/off
- **1/2**: Select ball/box spawning mode
- **+/-**: Increase/decrease spawn object size

### 🎯 Test Scenarios
- **Empty Scene**: Clean slate for experimentation
- **Basic Drop**: Simple falling objects test
- **Tower Stack**: Stability testing with stacked boxes
- **Pyramid**: Complex structure testing
- **Avalanche**: Unstable pile simulation
- **Stress Test**: Performance testing with 100+ objects
- **Collision Test**: Multi-object collision scenarios
- **Stability Test**: Complex structure stability
- **Zero Gravity**: Weightless environment testing
- **High Gravity**: Enhanced gravity simulation

### 📊 Performance Monitoring
- **Real-time FPS tracking**
- **Physics step timing**
- **Memory usage estimation**
- **Performance plots and metrics**
- **Comprehensive benchmarking suite**

### 🛠️ Debug Interface
- **Immediate mode GUI** using egui
- **Performance metrics display**
- **Physics parameter controls**
- **Object spawner with size control**
- **Simulation pause/step controls**
- **Test scenario launcher**
- **Help system with keyboard shortcuts**

## Performance Metrics

Based on the latest benchmark results:

### Core Operations
- **Physics World Creation**: ~953 ns
- **Physics Step (1 object)**: ~805 ns
- **Physics Step (10 objects)**: ~1.74 µs
- **Physics Step (50 objects)**: ~7.20 µs
- **Physics Step (100 objects)**: ~15.3 µs
- **Physics Step (200 objects)**: ~47.6 µs

### Object Operations
- **Ball Creation**: ~1.00 µs
- **Box Creation**: ~968 ns
- **Object Removal**: ~11.5 µs
- **Impulse Application**: ~18.0 ns

### Complex Scenarios
- **Collision Detection (low density)**: ~73.0 µs
- **Collision Detection (medium density)**: ~415 µs
- **Collision Detection (high density)**: ~1.17 ms
- **Stress Test (100 objects, 100 steps)**: ~3.86 ms
- **Memory Patterns (create/destroy cycle)**: ~106 µs

## Building and Running

### Prerequisites
- Rust 1.70+ with Cargo
- Graphics drivers supporting Vulkan, Metal, or DirectX 12

### Build Commands
```bash
# Build the project
cargo build --release

# Run the application
cargo run --release

# Run benchmarks
cargo bench

# Run tests
cargo test
```

### Development Build
```bash
# Quick development build
cargo run

# Check for compilation errors
cargo check
```

## Usage

### Basic Operation
1. **Launch the application**: `cargo run`
2. **Spawn objects**: Left-click anywhere in the window
3. **Change object type**: Press `1` for balls, `2` for boxes
4. **Adjust size**: Use `+`/`-` keys
5. **Control simulation**: `SPACE` to pause, `R` to clear, `G` to toggle gravity

### Test Scenarios
1. **Open Test Scenarios panel**: View menu → Test Scenarios
2. **Select a scenario**: Click any scenario button
3. **Auto-run mode**: Enable checkbox for automatic scenario cycling
4. **Quick actions**: Use preset buttons for common scenarios

### Performance Analysis
1. **View Performance panel**: View menu → Performance
2. **Monitor metrics**: Real-time FPS, physics timing, memory usage
3. **Run benchmarks**: `cargo bench` for detailed performance analysis

## Technical Details

### Dependencies
- **rapier2d**: 2D physics engine
- **wgpu**: Cross-platform graphics API
- **egui**: Immediate mode GUI
- **winit**: Window creation and event handling
- **nalgebra**: Linear algebra library
- **criterion**: Benchmarking framework

### Architecture Patterns
- **Entity-Component System**: For physics object management
- **Immediate Mode GUI**: For debug interface
- **Command Pattern**: For rendering operations
- **Observer Pattern**: For performance monitoring

## Contributing

This project demonstrates best practices for:
- High-performance Rust development
- Real-time physics simulation
- Interactive application design
- Comprehensive testing and benchmarking
- Performance optimization techniques

## License

This project is open source and available under the MIT License.