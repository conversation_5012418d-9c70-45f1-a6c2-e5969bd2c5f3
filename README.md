# Verturion - winit + wgpu Window Application

A simple Rust application demonstrating window creation and basic graphics rendering using the latest versions of winit and wgpu libraries.

## Features

- ✅ Cross-platform window creation using winit 0.30.11
- ✅ Modern graphics rendering using wgpu 26.0.1
- ✅ Event handling (resize, keyboard input, close)
- ✅ Basic rendering pipeline with a blue triangle
- ✅ Proper error handling and resource management
- ✅ Clean, documented code following Rust best practices

## Dependencies

- **winit 0.30.11** - Cross-platform window creation and event handling
- **wgpu 26.0.1** - Safe, cross-platform graphics API based on WebGPU
- **pollster 0.4.0** - Async runtime for blocking on futures

## Building and Running

### Prerequisites

- Rust 1.80+ (due to wgpu MSRV requirements)
- A graphics driver that supports Vulkan, Metal, D3D12, or OpenGL

### Build

```bash
cargo build
```

### Run

```bash
cargo run
```

### Test

```bash
cargo test
```

## Controls

- **Escape Key**: Exit the application
- **Window Close Button**: Exit the application
- **Window Resize**: The application responds to window resize events

## Project Structure

```
src/
├── main.rs          # Application entry point
├── lib.rs           # Main application logic and graphics setup
└── shader.wgsl      # WGSL shader for rendering a blue triangle
```

## Architecture

The application follows a clean architecture with:

1. **Graphics Context** (`Graphics` struct) - Manages all wgpu resources
2. **Application State** (`Application` struct) - Handles application lifecycle
3. **Event Handling** - Uses winit's `ApplicationHandler` trait for modern event handling
4. **Render Pipeline** - Simple vertex/fragment shader pipeline for triangle rendering

## Technical Details

- Uses the latest winit 0.30 API with `ApplicationHandler` trait
- Implements wgpu 26.0 API with proper error handling
- WGSL shaders for cross-platform compatibility
- Async graphics initialization with pollster for blocking
- Proper surface configuration and resize handling

## Graphics Pipeline

The application renders a simple blue triangle using:
- Vertex shader that generates triangle vertices procedurally
- Fragment shader that outputs a blue color
- Clear color background (dark blue-gray)

## License

This project is created as a demonstration and learning example.
