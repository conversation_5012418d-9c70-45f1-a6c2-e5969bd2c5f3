// Uniform buffer for transform matrix
struct TransformUniform {
    transform: mat4x4<f32>,
}

@group(0) @binding(0)
var<uniform> transform_uniform: TransformUniform;

// Uniform buffer for color
struct ColorUniform {
    color: vec4<f32>,
}

@group(0) @binding(1)
var<uniform> color_uniform: ColorUniform;

// Vertex input structure
struct VertexInput {
    @location(0) position: vec2<f32>,
}

// Vertex output structure
struct VertexOutput {
    @builtin(position) clip_position: vec4<f32>,
}

// Vertex shader
@vertex
fn vs_main(vertex: VertexInput) -> VertexOutput {
    var out: VertexOutput;

    // Transform the vertex position
    let world_position = transform_uniform.transform * vec4<f32>(vertex.position, 0.0, 1.0);
    out.clip_position = world_position;

    return out;
}

// Fragment shader
@fragment
fn fs_main(in: VertexOutput) -> @location(0) vec4<f32> {
    return color_uniform.color;
}
