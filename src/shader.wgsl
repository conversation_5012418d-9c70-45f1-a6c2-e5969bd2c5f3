// Vertex shader
@vertex
fn vs_main(@builtin(vertex_index) in_vertex_index: u32) -> @builtin(position) vec4<f32> {
    // Create a triangle with vertices at:
    // Top: (0.0, 0.5)
    // Bottom-left: (-0.5, -0.5)
    // Bottom-right: (0.5, -0.5)
    var pos = array<vec2<f32>, 3>(
        vec2<f32>(0.0, 0.5),
        vec2<f32>(-0.5, -0.5),
        vec2<f32>(0.5, -0.5)
    );
    
    return vec4<f32>(pos[in_vertex_index], 0.0, 1.0);
}

// Fragment shader
@fragment
fn fs_main() -> @location(0) vec4<f32> {
    // Return a nice blue color
    return vec4<f32>(0.3, 0.6, 1.0, 1.0);
}
