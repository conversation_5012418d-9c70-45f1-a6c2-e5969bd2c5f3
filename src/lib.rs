use std::sync::Arc;
use wgpu::{Adapter, Device, Instance, Queue, Surface, SurfaceConfiguration};
use winit::{
    application::ApplicationHandler,
    dpi::PhysicalSize,
    event::WindowEvent,
    event_loop::{ActiveEventLoop, EventLoop},
    window::{Window, WindowId},
};

pub mod scene_graph;
pub mod renderer;
pub mod test_scene;
pub mod animation;
pub mod input;
pub mod resources;

use scene_graph::*;
use renderer::SceneRenderer;
use animation::AnimationSystem;
use input::InputSystem;
use resources::ResourceSystem;

/// Graphics context containing all wgpu resources
pub struct Graphics {
    pub window: Arc<Window>,
    pub instance: Instance,
    pub surface: Surface<'static>,
    pub surface_config: SurfaceConfiguration,
    pub adapter: Adapter,
    pub device: Device,
    pub queue: Queue,
    pub scene_renderer: SceneRenderer,
}

impl Graphics {
    /// Create a new graphics context
    pub async fn new(window: Arc<Window>) -> Result<Self, Box<dyn std::error::Error>> {
        let instance = wgpu::Instance::default();
        
        let surface = instance.create_surface(window.clone())?;
        
        let adapter = instance
            .request_adapter(&wgpu::RequestAdapterOptions {
                compatible_surface: Some(&surface),
                power_preference: wgpu::PowerPreference::default(),
                force_fallback_adapter: false,
            })
            .await?;

        let (device, queue) = adapter
            .request_device(&wgpu::DeviceDescriptor {
                label: Some("Device"),
                required_features: wgpu::Features::empty(),
                required_limits: wgpu::Limits::default(),
                memory_hints: wgpu::MemoryHints::default(),
                ..Default::default()
            })
            .await?;

        let size = window.inner_size();
        let surface_config = surface
            .get_default_config(&adapter, size.width, size.height)
            .ok_or("Failed to get default surface configuration")?;

        surface.configure(&device, &surface_config);

        // Create scene renderer
        let scene_renderer = SceneRenderer::new(&device, surface_config.format);

        Ok(Self {
            window,
            instance,
            surface,
            surface_config,
            adapter,
            device,
            queue,
            scene_renderer,
        })
    }

    /// Resize the surface
    pub fn resize(&mut self, new_size: PhysicalSize<u32>) {
        if new_size.width > 0 && new_size.height > 0 {
            self.surface_config.width = new_size.width;
            self.surface_config.height = new_size.height;
            self.surface.configure(&self.device, &self.surface_config);
        }
    }

    /// Render a scene
    pub fn render_scene(&mut self, scene: &Scene) -> Result<(), wgpu::SurfaceError> {
        // Update the scene renderer with current scene data
        self.scene_renderer.update_scene(&self.device, scene);

        let output = self.surface.get_current_texture()?;
        let view = output
            .texture
            .create_view(&wgpu::TextureViewDescriptor::default());

        let mut encoder = self
            .device
            .create_command_encoder(&wgpu::CommandEncoderDescriptor {
                label: Some("Render Encoder"),
            });

        {
            let mut render_pass = encoder.begin_render_pass(&wgpu::RenderPassDescriptor {
                label: Some("Render Pass"),
                color_attachments: &[Some(wgpu::RenderPassColorAttachment {
                    view: &view,
                    resolve_target: None,
                    ops: wgpu::Operations {
                        load: wgpu::LoadOp::Clear(wgpu::Color {
                            r: 0.1,
                            g: 0.2,
                            b: 0.3,
                            a: 1.0,
                        }),
                        store: wgpu::StoreOp::Store,
                    },
                    depth_slice: None,
                })],
                depth_stencil_attachment: None,
                occlusion_query_set: None,
                timestamp_writes: None,
            });

            // Render the scene
            self.scene_renderer.render_scene(scene, &mut render_pass);
        }

        self.queue.submit(std::iter::once(encoder.finish()));
        output.present();

        Ok(())
    }
}

/// Application state
pub struct Application {
    graphics: Option<Graphics>,
    scene_manager: SceneManager,
    packed_scene_manager: PackedSceneManager,
    animation_system: AnimationSystem,
    input_system: InputSystem,
    resource_system: ResourceSystem,
}

impl Application {
    /// Create a new application
    pub fn new() -> Self {
        Self {
            graphics: None,
            scene_manager: SceneManager::new(),
            packed_scene_manager: PackedSceneManager::new(),
            animation_system: AnimationSystem::new(),
            input_system: InputSystem::new(),
            resource_system: ResourceSystem::new(),
        }
    }

    /// Get the scene manager
    pub fn scene_manager(&self) -> &SceneManager {
        &self.scene_manager
    }

    /// Get the scene manager mutably
    pub fn scene_manager_mut(&mut self) -> &mut SceneManager {
        &mut self.scene_manager
    }

    /// Get the packed scene manager
    pub fn packed_scene_manager(&self) -> &PackedSceneManager {
        &self.packed_scene_manager
    }

    /// Get the packed scene manager mutably
    pub fn packed_scene_manager_mut(&mut self) -> &mut PackedSceneManager {
        &mut self.packed_scene_manager
    }

    /// Load and change to a scene from a file
    pub fn change_scene(&mut self, path: &str) -> Result<(), SceneLoadError> {
        self.packed_scene_manager.change_scene(path)
    }

    /// Load and change to a packed scene
    pub fn change_scene_to_packed(&mut self, packed_scene: &PackedScene) -> Result<(), SceneLoadError> {
        self.packed_scene_manager.change_scene_to_packed(packed_scene)
    }

    /// Save the current scene to a file
    pub fn save_current_scene(&self, path: &str) -> Result<(), SceneLoadError> {
        self.packed_scene_manager.save_current_scene(path)
    }

    /// Initialize the comprehensive test scene
    fn initialize_test_scene(&mut self) {
        use test_scene::*;

        // Create a comprehensive test scene demonstrating all Godot-style features
        let mut scene = create_test_scene();

        // Demonstrate scene operations
        demonstrate_scene_operations(&mut scene);

        // Test lifecycle methods
        test_lifecycle_methods(&mut scene);

        // Add the scene to the scene manager
        self.scene_manager.add_scene(scene);

        // Test SceneTree functionality
        test_scene_tree();

        println!("\n=== Comprehensive Godot-Style Scene Graph System Initialized ===");
        println!("Features demonstrated:");
        println!("  ✓ Node hierarchy (Node → CanvasItem → Node2D → Sprite2D)");
        println!("  ✓ Specialized node types (RigidBody2D, Area2D, CharacterBody2D, Control, Label, Button)");
        println!("  ✓ Groups system for organizing nodes");
        println!("  ✓ Signals system for node communication");
        println!("  ✓ Lifecycle methods (_enter_tree, _ready, _process, _physics_process, _exit_tree)");
        println!("  ✓ Notification system");
        println!("  ✓ Z-index ordering for rendering");
        println!("  ✓ SceneTree management with pause/unpause");
        println!("  ✓ Process modes and flags");
        println!("  ✓ Transform hierarchy");
        println!("  ✓ CanvasItem rendering system");
    }
}

impl Default for Application {
    fn default() -> Self {
        Self::new()
    }
}

impl ApplicationHandler for Application {
    fn resumed(&mut self, event_loop: &ActiveEventLoop) {
        if self.graphics.is_none() {
            let window_attributes = Window::default_attributes()
                .with_title("Verturion - Scene Graph Demo")
                .with_inner_size(PhysicalSize::new(800, 600));

            let window = Arc::new(
                event_loop
                    .create_window(window_attributes)
                    .expect("Failed to create window"),
            );

            // Initialize graphics context asynchronously
            let graphics_future = Graphics::new(window);
            self.graphics = Some(pollster::block_on(graphics_future).expect("Failed to initialize graphics"));

            // Initialize the test scene
            self.initialize_test_scene();

            // Request initial redraw
            if let Some(graphics) = &self.graphics {
                graphics.window.request_redraw();
            }
        }
    }

    fn window_event(
        &mut self,
        event_loop: &ActiveEventLoop,
        _window_id: WindowId,
        event: WindowEvent,
    ) {
        // Process input events first
        self.input_system.process_event(&event);

        match event {
            WindowEvent::CloseRequested => {
                println!("Window close requested. Exiting...");
                event_loop.exit();
            }
            WindowEvent::Resized(physical_size) => {
                println!("Window resized to: {}x{}", physical_size.width, physical_size.height);
                if let Some(graphics) = &mut self.graphics {
                    graphics.resize(physical_size);
                }
            }
            WindowEvent::RedrawRequested => {
                if let Some(graphics) = &mut self.graphics {
                    // Update the input system
                    self.input_system.update();

                    // Update the animation system
                    self.animation_system.update(0.016); // Assume 60 FPS for now

                    // Update the scene
                    self.scene_manager.update(0.016);

                    // Render the active scene
                    let render_result = if let Some(scene) = self.scene_manager.get_active_scene() {
                        graphics.render_scene(scene)
                    } else {
                        // If no scene is active, just clear the screen
                        graphics.render_scene(&Scene::new("Empty".to_string()))
                    };

                    match render_result {
                        Ok(_) => {}
                        Err(wgpu::SurfaceError::Lost) => {
                            println!("Surface lost, reconfiguring...");
                            // Reconfigure the surface if lost
                            graphics.surface.configure(&graphics.device, &graphics.surface_config);
                        }
                        Err(wgpu::SurfaceError::OutOfMemory) => {
                            eprintln!("Out of memory! Exiting...");
                            event_loop.exit();
                        }
                        Err(e) => {
                            eprintln!("Render error: {e:?}");
                        }
                    }
                }

                // Continue requesting redraws for animation
                if let Some(graphics) = &self.graphics {
                    graphics.window.request_redraw();
                }
            }
            WindowEvent::KeyboardInput { event, .. } => {
                if event.state.is_pressed() {
                    println!("Key pressed: {:?}", event.logical_key);
                    // Exit on Escape key
                    if matches!(event.logical_key, winit::keyboard::Key::Named(winit::keyboard::NamedKey::Escape)) {
                        println!("Escape key pressed. Exiting...");
                        event_loop.exit();
                    }
                }
            }
            _ => {}
        }
    }
}

/// Run the application
pub fn run() -> Result<(), Box<dyn std::error::Error>> {
    let event_loop = EventLoop::new()?;
    let mut app = Application::new();
    
    event_loop.run_app(&mut app)?;
    
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_application_creation() {
        let app = Application::new();
        assert!(app.graphics.is_none());
    }
}
