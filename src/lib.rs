use std::sync::Arc;
use wgpu::{Adapter, Device, Instance, Queue, Surface, SurfaceConfiguration};
use winit::{
    application::ApplicationHandler,
    dpi::PhysicalSize,
    event::WindowEvent,
    event_loop::{ActiveEventLoop, EventLoop},
    window::{Window, WindowId},
};

pub mod scene_graph;
pub mod renderer;

use scene_graph::*;
use renderer::SceneRenderer;

/// Graphics context containing all wgpu resources
pub struct Graphics {
    pub window: Arc<Window>,
    pub instance: Instance,
    pub surface: Surface<'static>,
    pub surface_config: SurfaceConfiguration,
    pub adapter: Adapter,
    pub device: Device,
    pub queue: Queue,
    pub scene_renderer: SceneRenderer,
}

impl Graphics {
    /// Create a new graphics context
    pub async fn new(window: Arc<Window>) -> Result<Self, Box<dyn std::error::Error>> {
        let instance = wgpu::Instance::default();
        
        let surface = instance.create_surface(window.clone())?;
        
        let adapter = instance
            .request_adapter(&wgpu::RequestAdapterOptions {
                compatible_surface: Some(&surface),
                power_preference: wgpu::PowerPreference::default(),
                force_fallback_adapter: false,
            })
            .await?;

        let (device, queue) = adapter
            .request_device(&wgpu::DeviceDescriptor {
                label: Some("Device"),
                required_features: wgpu::Features::empty(),
                required_limits: wgpu::Limits::default(),
                memory_hints: wgpu::MemoryHints::default(),
                ..Default::default()
            })
            .await?;

        let size = window.inner_size();
        let surface_config = surface
            .get_default_config(&adapter, size.width, size.height)
            .ok_or("Failed to get default surface configuration")?;

        surface.configure(&device, &surface_config);

        // Create scene renderer
        let scene_renderer = SceneRenderer::new(&device, surface_config.format);

        Ok(Self {
            window,
            instance,
            surface,
            surface_config,
            adapter,
            device,
            queue,
            scene_renderer,
        })
    }

    /// Resize the surface
    pub fn resize(&mut self, new_size: PhysicalSize<u32>) {
        if new_size.width > 0 && new_size.height > 0 {
            self.surface_config.width = new_size.width;
            self.surface_config.height = new_size.height;
            self.surface.configure(&self.device, &self.surface_config);
        }
    }

    /// Render a scene
    pub fn render_scene(&mut self, scene: &Scene) -> Result<(), wgpu::SurfaceError> {
        // Update the scene renderer with current scene data
        self.scene_renderer.update_scene(&self.device, scene);

        let output = self.surface.get_current_texture()?;
        let view = output
            .texture
            .create_view(&wgpu::TextureViewDescriptor::default());

        let mut encoder = self
            .device
            .create_command_encoder(&wgpu::CommandEncoderDescriptor {
                label: Some("Render Encoder"),
            });

        {
            let mut render_pass = encoder.begin_render_pass(&wgpu::RenderPassDescriptor {
                label: Some("Render Pass"),
                color_attachments: &[Some(wgpu::RenderPassColorAttachment {
                    view: &view,
                    resolve_target: None,
                    ops: wgpu::Operations {
                        load: wgpu::LoadOp::Clear(wgpu::Color {
                            r: 0.1,
                            g: 0.2,
                            b: 0.3,
                            a: 1.0,
                        }),
                        store: wgpu::StoreOp::Store,
                    },
                    depth_slice: None,
                })],
                depth_stencil_attachment: None,
                occlusion_query_set: None,
                timestamp_writes: None,
            });

            // Render the scene
            self.scene_renderer.render_scene(scene, &mut render_pass);
        }

        self.queue.submit(std::iter::once(encoder.finish()));
        output.present();

        Ok(())
    }
}

/// Application state
pub struct Application {
    graphics: Option<Graphics>,
    scene_manager: SceneManager,
}

impl Application {
    /// Create a new application
    pub fn new() -> Self {
        Self {
            graphics: None,
            scene_manager: SceneManager::new(),
        }
    }

    /// Initialize the test scene with a triangle in the top-left corner
    fn initialize_test_scene(&mut self) {
        use nalgebra::Vector2;
        use scene_graph::node::Sprite2D;

        // Create a new scene
        let mut scene = Scene::new("Test Scene".to_string());

        // Create a triangle node positioned in the top-left corner
        // Convert top-left screen position to NDC coordinates
        let top_left_ndc = Vector2::new(-0.8, 0.8); // Top-left in NDC space
        let triangle_node = Sprite2D::triangle_with_position(
            "Triangle".to_string(),
            top_left_ndc,
            0.2, // Size
            [0.3, 0.6, 1.0, 1.0], // Blue color
        );

        // Add the node to the scene
        let _triangle_id = scene.add_node(Box::new(triangle_node));

        // Add the scene to the scene manager
        self.scene_manager.add_scene(scene);
    }
}

impl Default for Application {
    fn default() -> Self {
        Self::new()
    }
}

impl ApplicationHandler for Application {
    fn resumed(&mut self, event_loop: &ActiveEventLoop) {
        if self.graphics.is_none() {
            let window_attributes = Window::default_attributes()
                .with_title("Verturion - Scene Graph Demo")
                .with_inner_size(PhysicalSize::new(800, 600));

            let window = Arc::new(
                event_loop
                    .create_window(window_attributes)
                    .expect("Failed to create window"),
            );

            // Initialize graphics context asynchronously
            let graphics_future = Graphics::new(window);
            self.graphics = Some(pollster::block_on(graphics_future).expect("Failed to initialize graphics"));

            // Initialize the test scene
            self.initialize_test_scene();

            // Request initial redraw
            if let Some(graphics) = &self.graphics {
                graphics.window.request_redraw();
            }
        }
    }

    fn window_event(
        &mut self,
        event_loop: &ActiveEventLoop,
        _window_id: WindowId,
        event: WindowEvent,
    ) {
        match event {
            WindowEvent::CloseRequested => {
                println!("Window close requested. Exiting...");
                event_loop.exit();
            }
            WindowEvent::Resized(physical_size) => {
                println!("Window resized to: {}x{}", physical_size.width, physical_size.height);
                if let Some(graphics) = &mut self.graphics {
                    graphics.resize(physical_size);
                }
            }
            WindowEvent::RedrawRequested => {
                if let Some(graphics) = &mut self.graphics {
                    // Update the scene
                    self.scene_manager.update(0.016); // Assume 60 FPS for now

                    // Render the active scene
                    let render_result = if let Some(scene) = self.scene_manager.get_active_scene() {
                        graphics.render_scene(scene)
                    } else {
                        // If no scene is active, just clear the screen
                        graphics.render_scene(&Scene::new("Empty".to_string()))
                    };

                    match render_result {
                        Ok(_) => {}
                        Err(wgpu::SurfaceError::Lost) => {
                            println!("Surface lost, reconfiguring...");
                            // Reconfigure the surface if lost
                            graphics.surface.configure(&graphics.device, &graphics.surface_config);
                        }
                        Err(wgpu::SurfaceError::OutOfMemory) => {
                            eprintln!("Out of memory! Exiting...");
                            event_loop.exit();
                        }
                        Err(e) => {
                            eprintln!("Render error: {e:?}");
                        }
                    }
                }

                // Continue requesting redraws for animation
                if let Some(graphics) = &self.graphics {
                    graphics.window.request_redraw();
                }
            }
            WindowEvent::KeyboardInput { event, .. } => {
                if event.state.is_pressed() {
                    println!("Key pressed: {:?}", event.logical_key);
                    // Exit on Escape key
                    if matches!(event.logical_key, winit::keyboard::Key::Named(winit::keyboard::NamedKey::Escape)) {
                        println!("Escape key pressed. Exiting...");
                        event_loop.exit();
                    }
                }
            }
            _ => {}
        }
    }
}

/// Run the application
pub fn run() -> Result<(), Box<dyn std::error::Error>> {
    let event_loop = EventLoop::new()?;
    let mut app = Application::new();
    
    event_loop.run_app(&mut app)?;
    
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_application_creation() {
        let app = Application::new();
        assert!(app.graphics.is_none());
    }
}
