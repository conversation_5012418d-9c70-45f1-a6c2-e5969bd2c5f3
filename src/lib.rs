use std::sync::Arc;
use wgpu::{<PERSON><PERSON><PERSON>, Device, Instance, Queue, RenderPipeline, Surface, SurfaceConfiguration};
use winit::{
    application::ApplicationHandler,
    dpi::PhysicalSize,
    event::{WindowEvent, MouseButton, ElementState, KeyEvent},
    event_loop::{ActiveEventLoop, EventLoop},
    window::{Window, WindowId},
    keyboard::{KeyC<PERSON>, PhysicalKey},
};

pub mod physics;
pub mod debug_ui;
pub mod performance;

/// Graphics context containing all wgpu resources
pub struct Graphics {
    pub window: Arc<Window>,
    pub instance: Instance,
    pub surface: Surface<'static>,
    pub surface_config: SurfaceConfiguration,
    pub adapter: Adapter,
    pub device: Device,
    pub queue: Queue,
    pub render_pipeline: RenderPipeline,
}

impl Graphics {
    /// Create a new graphics context
    pub async fn new(window: Arc<Window>) -> Result<Self, Box<dyn std::error::Error>> {
        let instance = wgpu::Instance::default();
        
        let surface = instance.create_surface(window.clone())?;
        
        let adapter = instance
            .request_adapter(&wgpu::RequestAdapterOptions {
                compatible_surface: Some(&surface),
                power_preference: wgpu::PowerPreference::default(),
                force_fallback_adapter: false,
            })
            .await?;

        let (device, queue) = adapter
            .request_device(&wgpu::DeviceDescriptor {
                label: Some("Device"),
                required_features: wgpu::Features::empty(),
                required_limits: wgpu::Limits::default(),
                memory_hints: wgpu::MemoryHints::default(),
                ..Default::default()
            })
            .await?;

        let size = window.inner_size();
        let surface_config = surface
            .get_default_config(&adapter, size.width, size.height)
            .ok_or("Failed to get default surface configuration")?;

        surface.configure(&device, &surface_config);

        // Create shader module
        let shader = device.create_shader_module(wgpu::ShaderModuleDescriptor {
            label: Some("Triangle Shader"),
            source: wgpu::ShaderSource::Wgsl(std::borrow::Cow::Borrowed(include_str!("shader.wgsl"))),
        });

        // Create render pipeline
        let render_pipeline_layout = device.create_pipeline_layout(&wgpu::PipelineLayoutDescriptor {
            label: Some("Render Pipeline Layout"),
            bind_group_layouts: &[],
            push_constant_ranges: &[],
        });

        let render_pipeline = device.create_render_pipeline(&wgpu::RenderPipelineDescriptor {
            label: Some("Render Pipeline"),
            layout: Some(&render_pipeline_layout),
            vertex: wgpu::VertexState {
                module: &shader,
                entry_point: Some("vs_main"),
                buffers: &[],
                compilation_options: wgpu::PipelineCompilationOptions::default(),
            },
            fragment: Some(wgpu::FragmentState {
                module: &shader,
                entry_point: Some("fs_main"),
                targets: &[Some(wgpu::ColorTargetState {
                    format: surface_config.format,
                    blend: Some(wgpu::BlendState::REPLACE),
                    write_mask: wgpu::ColorWrites::ALL,
                })],
                compilation_options: wgpu::PipelineCompilationOptions::default(),
            }),
            primitive: wgpu::PrimitiveState {
                topology: wgpu::PrimitiveTopology::TriangleList,
                strip_index_format: None,
                front_face: wgpu::FrontFace::Ccw,
                cull_mode: Some(wgpu::Face::Back),
                polygon_mode: wgpu::PolygonMode::Fill,
                unclipped_depth: false,
                conservative: false,
            },
            depth_stencil: None,
            multisample: wgpu::MultisampleState {
                count: 1,
                mask: !0,
                alpha_to_coverage_enabled: false,
            },
            multiview: None,
            cache: None,
        });

        Ok(Self {
            window,
            instance,
            surface,
            surface_config,
            adapter,
            device,
            queue,
            render_pipeline,
        })
    }

    /// Resize the surface
    pub fn resize(&mut self, new_size: PhysicalSize<u32>) {
        if new_size.width > 0 && new_size.height > 0 {
            self.surface_config.width = new_size.width;
            self.surface_config.height = new_size.height;
            self.surface.configure(&self.device, &self.surface_config);
        }
    }

    /// Render a frame
    pub fn render(&self) -> Result<(), wgpu::SurfaceError> {
        let output = self.surface.get_current_texture()?;
        let view = output
            .texture
            .create_view(&wgpu::TextureViewDescriptor::default());

        let mut encoder = self
            .device
            .create_command_encoder(&wgpu::CommandEncoderDescriptor {
                label: Some("Render Encoder"),
            });

        {
            let mut render_pass = encoder.begin_render_pass(&wgpu::RenderPassDescriptor {
                label: Some("Render Pass"),
                color_attachments: &[Some(wgpu::RenderPassColorAttachment {
                    view: &view,
                    resolve_target: None,
                    ops: wgpu::Operations {
                        load: wgpu::LoadOp::Clear(wgpu::Color {
                            r: 0.1,
                            g: 0.2,
                            b: 0.3,
                            a: 1.0,
                        }),
                        store: wgpu::StoreOp::Store,
                    },
                })],
                depth_stencil_attachment: None,
                occlusion_query_set: None,
                timestamp_writes: None,
            });

            // Set the render pipeline and draw the triangle
            render_pass.set_pipeline(&self.render_pipeline);
            render_pass.draw(0..3, 0..1);
        }

        self.queue.submit(std::iter::once(encoder.finish()));
        output.present();

        Ok(())
    }
}

/// Application state
pub struct Application {
    graphics: Option<Graphics>,
    physics_world: Option<physics::PhysicsWorld>,
    performance_tracker: Option<performance::PerformanceTracker>,
    debug_ui: Option<debug_ui::DebugUI>,
    egui_context: Option<egui::Context>,
    egui_state: Option<egui_winit::State>,
    egui_renderer: Option<egui_wgpu::Renderer>,
    mouse_position: (f32, f32),
    mouse_pressed: bool,
    keys_pressed: std::collections::HashSet<KeyCode>,
}

impl Application {
    /// Create a new application
    pub fn new() -> Self {
        Self {
            graphics: None,
            physics_world: None,
            performance_tracker: None,
            debug_ui: None,
            egui_context: None,
            egui_state: None,
            egui_renderer: None,
            mouse_position: (0.0, 0.0),
            mouse_pressed: false,
            keys_pressed: std::collections::HashSet::new(),
        }
    }
}

impl Default for Application {
    fn default() -> Self {
        Self::new()
    }
}

impl ApplicationHandler for Application {
    fn resumed(&mut self, event_loop: &ActiveEventLoop) {
        if self.graphics.is_none() {
            let window_attributes = Window::default_attributes()
                .with_title("Verturion - 2D Physics Test with Benchmarks and Debug Tools")
                .with_inner_size(PhysicalSize::new(1200, 800));

            let window = Arc::new(
                event_loop
                    .create_window(window_attributes)
                    .expect("Failed to create window"),
            );

            // Initialize graphics context asynchronously
            let graphics_future = Graphics::new(window.clone());
            let graphics = pollster::block_on(graphics_future).expect("Failed to initialize graphics");

            // Initialize egui
            let egui_context = egui::Context::default();
            let egui_state = egui_winit::State::new(
                egui_context.clone(),
                egui::ViewportId::ROOT,
                &window,
                Some(window.scale_factor() as f32),
                None,
                Some(2048),
            );
            let egui_renderer = egui_wgpu::Renderer::new(
                &graphics.device,
                graphics.surface_config.format,
                None,
                1,
                false,
            );

            // Initialize physics world
            let mut physics_world = physics::PhysicsWorld::new();

            // Add some initial objects for demonstration
            physics_world.create_ball(nalgebra::Vector2::new(-3.0, 8.0), 0.5);
            physics_world.create_ball(nalgebra::Vector2::new(0.0, 10.0), 0.7);
            physics_world.create_ball(nalgebra::Vector2::new(3.0, 12.0), 0.6);
            physics_world.create_box(nalgebra::Vector2::new(-1.0, 6.0), nalgebra::Vector2::new(0.5, 0.5));
            physics_world.create_box(nalgebra::Vector2::new(1.0, 8.0), nalgebra::Vector2::new(0.7, 0.3));

            // Initialize performance tracker
            let performance_tracker = performance::PerformanceTracker::new(120);

            // Initialize debug UI
            let debug_ui = debug_ui::DebugUI::new();

            // Store all components
            self.graphics = Some(graphics);
            self.physics_world = Some(physics_world);
            self.performance_tracker = Some(performance_tracker);
            self.debug_ui = Some(debug_ui);
            self.egui_context = Some(egui_context);
            self.egui_state = Some(egui_state);
            self.egui_renderer = Some(egui_renderer);

            // Request initial redraw
            if let Some(graphics) = &self.graphics {
                graphics.window.request_redraw();
            }
        }
    }

    fn window_event(
        &mut self,
        event_loop: &ActiveEventLoop,
        _window_id: WindowId,
        event: WindowEvent,
    ) {
        // Handle egui events first
        if let (Some(egui_state), Some(graphics)) = (&mut self.egui_state, &self.graphics) {
            let _ = egui_state.on_window_event(&graphics.window, &event);
        }

        match event {
            WindowEvent::CloseRequested => {
                println!("Window close requested. Exiting...");
                event_loop.exit();
            }
            WindowEvent::Resized(physical_size) => {
                println!("Window resized to: {}x{}", physical_size.width, physical_size.height);
                if let Some(graphics) = &mut self.graphics {
                    graphics.resize(physical_size);
                }
            }
            WindowEvent::RedrawRequested => {
                self.update_and_render(event_loop);
            }
            WindowEvent::CursorMoved { position, .. } => {
                self.mouse_position = (position.x as f32, position.y as f32);
            }
            WindowEvent::MouseInput { state, button, .. } => {
                if button == MouseButton::Left {
                    self.mouse_pressed = state == ElementState::Pressed;

                    // Handle mouse click for object spawning
                    if self.mouse_pressed {
                        self.handle_mouse_click();
                    }
                }
            }
            WindowEvent::KeyboardInput { event, .. } => {
                self.handle_keyboard_input(event);

                // Check if escape was pressed to exit
                if self.should_exit() {
                    event_loop.exit();
                }
            }
            _ => {}
        }
    }
}

impl Application {
    /// Update physics and render the frame
    fn update_and_render(&mut self, event_loop: &ActiveEventLoop) {
        // Start frame timing
        if let Some(performance_tracker) = &mut self.performance_tracker {
            performance_tracker.start_frame();
        }

        // Update physics
        if let (Some(physics_world), Some(debug_ui), Some(performance_tracker)) =
            (&mut self.physics_world, &mut self.debug_ui, &mut self.performance_tracker) {

            if debug_ui.should_step_simulation() {
                performance_tracker.start_physics();
                physics_world.step();
                performance_tracker.end_physics();
            }
        }

        // Render
        if let Some(performance_tracker) = &mut self.performance_tracker {
            performance_tracker.start_render();
        }

        let render_result = self.render_frame();

        match render_result {
            Ok(_) => {}
            Err(wgpu::SurfaceError::Lost) => {
                println!("Surface lost, reconfiguring...");
                if let Some(graphics) = &self.graphics {
                    graphics.surface.configure(&graphics.device, &graphics.surface_config);
                }
            }
            Err(wgpu::SurfaceError::OutOfMemory) => {
                eprintln!("Out of memory! Exiting...");
                event_loop.exit();
            }
            Err(e) => {
                eprintln!("Render error: {e:?}");
            }
        }

        if let Some(performance_tracker) = &mut self.performance_tracker {
            performance_tracker.end_render();
        }

        // End frame timing
        if let Some(performance_tracker) = &mut self.performance_tracker {
            performance_tracker.end_frame();
        }

        // Continue requesting redraws for animation
        if let Some(graphics) = &self.graphics {
            graphics.window.request_redraw();
        }
    }

    /// Render a complete frame with physics objects and UI
    fn render_frame(&mut self) -> Result<(), wgpu::SurfaceError> {
        let (graphics, physics_world, performance_tracker, debug_ui, egui_context, egui_state, egui_renderer) =
            match (&self.graphics, &mut self.physics_world, &self.performance_tracker, &mut self.debug_ui,
                   &self.egui_context, &mut self.egui_state, &mut self.egui_renderer) {
                (Some(g), Some(p), Some(pt), Some(d), Some(ec), Some(es), Some(er)) => (g, p, pt, d, ec, es, er),
                _ => return Ok(()),
            };
        let output = graphics.surface.get_current_texture()?;
        let view = output
            .texture
            .create_view(&wgpu::TextureViewDescriptor::default());

        let mut encoder = graphics
            .device
            .create_command_encoder(&wgpu::CommandEncoderDescriptor {
                label: Some("Render Encoder"),
            });

        // Render physics objects
        {
            let mut render_pass = encoder.begin_render_pass(&wgpu::RenderPassDescriptor {
                label: Some("Physics Render Pass"),
                color_attachments: &[Some(wgpu::RenderPassColorAttachment {
                    view: &view,
                    resolve_target: None,
                    ops: wgpu::Operations {
                        load: wgpu::LoadOp::Clear(wgpu::Color {
                            r: 0.1,
                            g: 0.2,
                            b: 0.3,
                            a: 1.0,
                        }),
                        store: wgpu::StoreOp::Store,
                    },
                })],
                depth_stencil_attachment: None,
                occlusion_query_set: None,
                timestamp_writes: None,
            });

            // Set the render pipeline and draw physics objects
            render_pass.set_pipeline(&graphics.render_pipeline);

            // For now, just draw the original triangle
            // TODO: Replace with actual physics object rendering
            render_pass.draw(0..3, 0..1);
        }

        // TODO: Fix egui rendering - temporarily disabled due to lifetime issues
        // For now, just run the egui logic without rendering to test physics
        let raw_input = egui_state.take_egui_input(&graphics.window);
        let _full_output = egui_context.run(raw_input, |ctx| {
            debug_ui.render(ctx, physics_world, performance_tracker);
        });

        // Submit the physics rendering commands
        graphics.queue.submit(std::iter::once(encoder.finish()));

        output.present();

        Ok(())
    }

    /// Handle mouse click for object spawning
    fn handle_mouse_click(&mut self) {
        if let (Some(physics_world), Some(graphics), Some(debug_ui)) =
            (&mut self.physics_world, &self.graphics, &self.debug_ui) {

            // Convert screen coordinates to world coordinates
            let screen_width = graphics.surface_config.width as f32;
            let screen_height = graphics.surface_config.height as f32;

            // Simple screen-to-world coordinate conversion
            // Assuming a world coordinate system from -10 to 10 in X and -5 to 15 in Y
            let world_x = (self.mouse_position.0 / screen_width) * 20.0 - 10.0;
            let world_y = 15.0 - (self.mouse_position.1 / screen_height) * 20.0;

            let position = nalgebra::Vector2::new(world_x, world_y);

            // Spawn object based on current debug UI settings
            match debug_ui.spawn_object_type {
                physics::ObjectType::Ball => {
                    physics_world.create_ball(position, debug_ui.spawn_size);
                    println!("Spawned ball (radius: {:.1}) at world coordinates: ({:.2}, {:.2})",
                             debug_ui.spawn_size, world_x, world_y);
                }
                physics::ObjectType::Box => {
                    let half_extents = nalgebra::Vector2::new(debug_ui.spawn_size, debug_ui.spawn_size);
                    physics_world.create_box(position, half_extents);
                    println!("Spawned box (size: {:.1}) at world coordinates: ({:.2}, {:.2})",
                             debug_ui.spawn_size, world_x, world_y);
                }
                physics::ObjectType::Ground => {
                    // Ground objects are not spawnable via mouse
                    println!("Cannot spawn ground objects via mouse click");
                }
            }
        }
    }

    /// Handle keyboard input for various controls
    fn handle_keyboard_input(&mut self, event: KeyEvent) {
        if event.state.is_pressed() {
            if let PhysicalKey::Code(key_code) = event.physical_key {
                self.keys_pressed.insert(key_code);

                match key_code {
                    KeyCode::Escape => {
                        println!("Escape key pressed. Exiting...");
                        // Note: We can't access event_loop here, so we'll handle this in the caller
                    }
                    KeyCode::Space => {
                        if let Some(debug_ui) = &mut self.debug_ui {
                            debug_ui.paused = !debug_ui.paused;
                            println!("Physics simulation {}", if debug_ui.paused { "paused" } else { "resumed" });
                        }
                    }
                    KeyCode::KeyR => {
                        if let Some(physics_world) = &mut self.physics_world {
                            physics_world.clear_dynamic_objects();
                            println!("Cleared all dynamic objects");
                        }
                    }
                    KeyCode::KeyG => {
                        if let Some(physics_world) = &mut self.physics_world {
                            // Toggle gravity
                            let current_gravity = physics_world.gravity;
                            if current_gravity.y.abs() > 0.1 {
                                physics_world.set_gravity(nalgebra::Vector2::new(0.0, 0.0));
                                println!("Gravity disabled");
                            } else {
                                physics_world.set_gravity(nalgebra::Vector2::new(0.0, -9.81));
                                println!("Gravity enabled");
                            }
                        }
                    }
                    KeyCode::Digit1 => {
                        if let Some(debug_ui) = &mut self.debug_ui {
                            debug_ui.spawn_object_type = physics::ObjectType::Ball;
                            println!("Selected ball spawning mode");
                        }
                    }
                    KeyCode::Digit2 => {
                        if let Some(debug_ui) = &mut self.debug_ui {
                            debug_ui.spawn_object_type = physics::ObjectType::Box;
                            println!("Selected box spawning mode");
                        }
                    }
                    KeyCode::Equal | KeyCode::NumpadAdd => {
                        if let Some(debug_ui) = &mut self.debug_ui {
                            debug_ui.spawn_size = (debug_ui.spawn_size + 0.1).min(3.0);
                            println!("Spawn size increased to {:.1}", debug_ui.spawn_size);
                        }
                    }
                    KeyCode::Minus | KeyCode::NumpadSubtract => {
                        if let Some(debug_ui) = &mut self.debug_ui {
                            debug_ui.spawn_size = (debug_ui.spawn_size - 0.1).max(0.1);
                            println!("Spawn size decreased to {:.1}", debug_ui.spawn_size);
                        }
                    }
                    _ => {}
                }
            }
        } else {
            // Key released
            if let PhysicalKey::Code(key_code) = event.physical_key {
                self.keys_pressed.remove(&key_code);
            }
        }
    }

    /// Check if escape key was pressed (for event loop exit)
    fn should_exit(&self) -> bool {
        self.keys_pressed.contains(&KeyCode::Escape)
    }
}

/// Run the application
pub fn run() -> Result<(), Box<dyn std::error::Error>> {
    let event_loop = EventLoop::new()?;
    let mut app = Application::new();
    
    event_loop.run_app(&mut app)?;
    
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_application_creation() {
        let app = Application::new();
        assert!(app.graphics.is_none());
    }
}
