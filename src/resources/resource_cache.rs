use super::*;
use std::collections::HashMap;
use std::sync::{Arc, Weak};
use std::time::{Duration, Instant};

/// Resource cache for managing loaded resources
#[derive(Debug)]
pub struct ResourceCache {
    /// Cached resources by path
    resources: HashMap<String, CacheEntry>,
    /// Maximum cache size in bytes
    max_size: usize,
    /// Current cache size in bytes
    current_size: usize,
    /// Cache statistics
    stats: CacheStats,
}

impl ResourceCache {
    /// Create a new resource cache
    pub fn new() -> Self {
        Self {
            resources: HashMap::new(),
            max_size: 100 * 1024 * 1024, // 100 MB default
            current_size: 0,
            stats: CacheStats::default(),
        }
    }
    
    /// Create a new resource cache with custom max size
    pub fn with_max_size(max_size: usize) -> Self {
        Self {
            resources: HashMap::new(),
            max_size,
            current_size: 0,
            stats: CacheStats::default(),
        }
    }
    
    /// Insert a resource into the cache
    pub fn insert<T: Resource + 'static>(&mut self, path: String, resource: Arc<T>) {
        let size = resource.get_memory_usage();
        
        // Check if we need to evict resources
        self.ensure_capacity(size);
        
        let entry = CacheEntry {
            resource: resource.clone(),
            size,
            last_accessed: Instant::now(),
            access_count: 1,
        };
        
        // Remove old entry if it exists
        if let Some(old_entry) = self.resources.remove(&path) {
            self.current_size -= old_entry.size;
        }
        
        self.resources.insert(path, entry);
        self.current_size += size;
        self.stats.cache_misses += 1;
    }
    
    /// Get a resource from the cache
    pub fn get<T: Resource + 'static>(&mut self, path: &str) -> Option<Arc<T>> {
        if let Some(entry) = self.resources.get_mut(path) {
            entry.last_accessed = Instant::now();
            entry.access_count += 1;
            self.stats.cache_hits += 1;
            
            // Try to downcast to the requested type
            entry.resource.as_any()
                .downcast_ref::<T>()
                .map(|r| Arc::new(r.clone()))
        } else {
            self.stats.cache_misses += 1;
            None
        }
    }
    
    /// Remove a resource from the cache
    pub fn remove(&mut self, path: &str) -> bool {
        if let Some(entry) = self.resources.remove(path) {
            self.current_size -= entry.size;
            true
        } else {
            false
        }
    }
    
    /// Check if a resource is cached
    pub fn contains(&self, path: &str) -> bool {
        self.resources.contains_key(path)
    }
    
    /// Clear the entire cache
    pub fn clear(&mut self) {
        self.resources.clear();
        self.current_size = 0;
        self.stats.evictions += self.resources.len();
    }
    
    /// Ensure there's enough capacity for a new resource
    fn ensure_capacity(&mut self, needed_size: usize) {
        while self.current_size + needed_size > self.max_size && !self.resources.is_empty() {
            self.evict_lru();
        }
    }
    
    /// Evict the least recently used resource
    fn evict_lru(&mut self) {
        let mut oldest_path = None;
        let mut oldest_time = Instant::now();
        
        for (path, entry) in &self.resources {
            if entry.last_accessed < oldest_time {
                oldest_time = entry.last_accessed;
                oldest_path = Some(path.clone());
            }
        }
        
        if let Some(path) = oldest_path {
            self.remove(&path);
            self.stats.evictions += 1;
        }
    }
    
    /// Get cache statistics
    pub fn get_stats(&self) -> CacheStats {
        self.stats.clone()
    }
    
    /// Get current memory usage
    pub fn get_memory_usage(&self) -> usize {
        self.current_size
    }
    
    /// Get maximum cache size
    pub fn get_max_size(&self) -> usize {
        self.max_size
    }
    
    /// Set maximum cache size
    pub fn set_max_size(&mut self, max_size: usize) {
        self.max_size = max_size;
        self.ensure_capacity(0); // Trigger eviction if needed
    }
    
    /// Get all cached resource paths
    pub fn get_all_paths(&self) -> Vec<String> {
        self.resources.keys().cloned().collect()
    }
    
    /// Get cache utilization (0.0 to 1.0)
    pub fn get_utilization(&self) -> f32 {
        if self.max_size == 0 {
            0.0
        } else {
            self.current_size as f32 / self.max_size as f32
        }
    }
    
    /// Cleanup expired weak references
    pub fn cleanup(&mut self) {
        let mut to_remove = Vec::new();
        
        for (path, entry) in &self.resources {
            // Check if the resource is only held by the cache
            if Arc::strong_count(&entry.resource) == 1 {
                // Check if it's been unused for a while
                if entry.last_accessed.elapsed() > Duration::from_secs(300) { // 5 minutes
                    to_remove.push(path.clone());
                }
            }
        }
        
        for path in to_remove {
            self.remove(&path);
        }
    }
    
    /// Get resources sorted by access frequency
    pub fn get_most_accessed(&self, limit: usize) -> Vec<(String, usize)> {
        let mut entries: Vec<_> = self.resources
            .iter()
            .map(|(path, entry)| (path.clone(), entry.access_count))
            .collect();
        
        entries.sort_by(|a, b| b.1.cmp(&a.1));
        entries.truncate(limit);
        entries
    }
    
    /// Get resources sorted by size
    pub fn get_largest(&self, limit: usize) -> Vec<(String, usize)> {
        let mut entries: Vec<_> = self.resources
            .iter()
            .map(|(path, entry)| (path.clone(), entry.size))
            .collect();
        
        entries.sort_by(|a, b| b.1.cmp(&a.1));
        entries.truncate(limit);
        entries
    }
}

impl Default for ResourceCache {
    fn default() -> Self {
        Self::new()
    }
}

/// Cache entry for a resource
#[derive(Debug)]
struct CacheEntry {
    /// The cached resource
    resource: Arc<dyn Resource>,
    /// Size of the resource in bytes
    size: usize,
    /// Last time this resource was accessed
    last_accessed: Instant,
    /// Number of times this resource has been accessed
    access_count: usize,
}

/// Cache statistics
#[derive(Debug, Clone, Default)]
pub struct CacheStats {
    /// Number of cache hits
    pub cache_hits: usize,
    /// Number of cache misses
    pub cache_misses: usize,
    /// Number of evictions
    pub evictions: usize,
}

impl CacheStats {
    /// Get hit ratio (0.0 to 1.0)
    pub fn hit_ratio(&self) -> f32 {
        let total = self.cache_hits + self.cache_misses;
        if total == 0 {
            0.0
        } else {
            self.cache_hits as f32 / total as f32
        }
    }
    
    /// Reset statistics
    pub fn reset(&mut self) {
        self.cache_hits = 0;
        self.cache_misses = 0;
        self.evictions = 0;
    }
}

/// Weak resource cache for resources that can be garbage collected
#[derive(Debug, Default)]
pub struct WeakResourceCache {
    resources: HashMap<String, Weak<dyn Resource>>,
}

impl WeakResourceCache {
    /// Create a new weak resource cache
    pub fn new() -> Self {
        Self {
            resources: HashMap::new(),
        }
    }
    
    /// Insert a weak reference to a resource
    pub fn insert<T: Resource + 'static>(&mut self, path: String, resource: &Arc<T>) {
        self.resources.insert(path, Arc::downgrade(resource) as Weak<dyn Resource>);
    }
    
    /// Try to get a resource from weak references
    pub fn get<T: Resource + 'static>(&mut self, path: &str) -> Option<Arc<T>> {
        if let Some(weak_ref) = self.resources.get(path) {
            if let Some(resource) = weak_ref.upgrade() {
                // Try to downcast to the requested type
                resource.as_any()
                    .downcast_ref::<T>()
                    .map(|r| Arc::new(r.clone()))
            } else {
                // Resource has been dropped, remove the weak reference
                self.resources.remove(path);
                None
            }
        } else {
            None
        }
    }
    
    /// Cleanup expired weak references
    pub fn cleanup(&mut self) {
        self.resources.retain(|_, weak_ref| weak_ref.strong_count() > 0);
    }
    
    /// Get the number of cached weak references
    pub fn len(&self) -> usize {
        self.resources.len()
    }
    
    /// Check if the cache is empty
    pub fn is_empty(&self) -> bool {
        self.resources.is_empty()
    }
    
    /// Clear all weak references
    pub fn clear(&mut self) {
        self.resources.clear();
    }
}
