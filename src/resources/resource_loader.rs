use super::*;
use std::fs;
use std::sync::Arc;

/// Resource loader trait for loading different types of resources
pub trait ResourceLoader: Send + Sync {
    /// Load a resource from a file path
    fn load(&self, path: &str) -> Result<Arc<dyn Resource>, ResourceError>;
    
    /// Save a resource to a file path
    fn save(&self, resource: &dyn Resource, path: &str) -> Result<(), ResourceError>;
    
    /// Get supported file extensions
    fn get_supported_extensions(&self) -> Vec<String>;
    
    /// Check if this loader can handle the given file extension
    fn can_load(&self, extension: &str) -> bool {
        self.get_supported_extensions()
            .iter()
            .any(|ext| ext.eq_ignore_ascii_case(extension))
    }
}

/// Image resource loader
pub struct ImageLoader;

impl ImageLoader {
    pub fn new() -> Self {
        Self
    }
}

impl ResourceLoader for ImageLoader {
    fn load(&self, path: &str) -> Result<Arc<dyn Resource>, ResourceError> {
        // In a real implementation, this would use an image loading library like `image`
        // For now, we'll create a placeholder image resource
        
        if !std::path::Path::new(path).exists() {
            return Err(ResourceError::FileNotFound(path.to_string()));
        }
        
        // Read file data
        let data = fs::read(path)
            .map_err(|e| ResourceError::IoError(e.to_string()))?;
        
        let image = ImageResource {
            path: path.to_string(),
            width: 256, // Placeholder dimensions
            height: 256,
            format: ImageFormat::RGBA8,
            data,
        };
        
        Ok(Arc::new(image))
    }
    
    fn save(&self, resource: &dyn Resource, path: &str) -> Result<(), ResourceError> {
        if let Some(image) = resource.as_any().downcast_ref::<ImageResource>() {
            fs::write(path, &image.data)
                .map_err(|e| ResourceError::IoError(e.to_string()))?;
            Ok(())
        } else {
            Err(ResourceError::TypeMismatch {
                expected: "ImageResource".to_string(),
                actual: resource.get_type_name().to_string(),
            })
        }
    }
    
    fn get_supported_extensions(&self) -> Vec<String> {
        vec!["png".to_string(), "jpg".to_string(), "jpeg".to_string(), "bmp".to_string()]
    }
}

/// Audio resource loader
pub struct AudioLoader;

impl AudioLoader {
    pub fn new() -> Self {
        Self
    }
}

impl ResourceLoader for AudioLoader {
    fn load(&self, path: &str) -> Result<Arc<dyn Resource>, ResourceError> {
        if !std::path::Path::new(path).exists() {
            return Err(ResourceError::FileNotFound(path.to_string()));
        }
        
        let data = fs::read(path)
            .map_err(|e| ResourceError::IoError(e.to_string()))?;
        
        let audio = AudioResource {
            path: path.to_string(),
            sample_rate: 44100,
            channels: 2,
            format: AudioFormat::F32,
            data,
        };
        
        Ok(Arc::new(audio))
    }
    
    fn save(&self, resource: &dyn Resource, path: &str) -> Result<(), ResourceError> {
        if let Some(audio) = resource.as_any().downcast_ref::<AudioResource>() {
            fs::write(path, &audio.data)
                .map_err(|e| ResourceError::IoError(e.to_string()))?;
            Ok(())
        } else {
            Err(ResourceError::TypeMismatch {
                expected: "AudioResource".to_string(),
                actual: resource.get_type_name().to_string(),
            })
        }
    }
    
    fn get_supported_extensions(&self) -> Vec<String> {
        vec!["wav".to_string(), "ogg".to_string(), "mp3".to_string(), "flac".to_string()]
    }
}

/// JSON resource loader
pub struct JsonLoader;

impl JsonLoader {
    pub fn new() -> Self {
        Self
    }
}

impl ResourceLoader for JsonLoader {
    fn load(&self, path: &str) -> Result<Arc<dyn Resource>, ResourceError> {
        if !std::path::Path::new(path).exists() {
            return Err(ResourceError::FileNotFound(path.to_string()));
        }
        
        let content = fs::read_to_string(path)
            .map_err(|e| ResourceError::IoError(e.to_string()))?;
        
        // In a real implementation, this would parse JSON
        let json = JsonResource {
            path: path.to_string(),
            content,
        };
        
        Ok(Arc::new(json))
    }
    
    fn save(&self, resource: &dyn Resource, path: &str) -> Result<(), ResourceError> {
        if let Some(json) = resource.as_any().downcast_ref::<JsonResource>() {
            fs::write(path, &json.content)
                .map_err(|e| ResourceError::IoError(e.to_string()))?;
            Ok(())
        } else {
            Err(ResourceError::TypeMismatch {
                expected: "JsonResource".to_string(),
                actual: resource.get_type_name().to_string(),
            })
        }
    }
    
    fn get_supported_extensions(&self) -> Vec<String> {
        vec!["json".to_string()]
    }
}

/// Scene resource loader
pub struct SceneLoader;

impl SceneLoader {
    pub fn new() -> Self {
        Self
    }
}

impl ResourceLoader for SceneLoader {
    fn load(&self, path: &str) -> Result<Arc<dyn Resource>, ResourceError> {
        if !std::path::Path::new(path).exists() {
            return Err(ResourceError::FileNotFound(path.to_string()));
        }
        
        let content = fs::read_to_string(path)
            .map_err(|e| ResourceError::IoError(e.to_string()))?;
        
        let scene = SceneResource {
            path: path.to_string(),
            content,
        };
        
        Ok(Arc::new(scene))
    }
    
    fn save(&self, resource: &dyn Resource, path: &str) -> Result<(), ResourceError> {
        if let Some(scene) = resource.as_any().downcast_ref::<SceneResource>() {
            fs::write(path, &scene.content)
                .map_err(|e| ResourceError::IoError(e.to_string()))?;
            Ok(())
        } else {
            Err(ResourceError::TypeMismatch {
                expected: "SceneResource".to_string(),
                actual: resource.get_type_name().to_string(),
            })
        }
    }
    
    fn get_supported_extensions(&self) -> Vec<String> {
        vec!["tscn".to_string(), "scn".to_string()]
    }
}

/// Font resource loader
pub struct FontLoader;

impl FontLoader {
    pub fn new() -> Self {
        Self
    }
}

impl ResourceLoader for FontLoader {
    fn load(&self, path: &str) -> Result<Arc<dyn Resource>, ResourceError> {
        if !std::path::Path::new(path).exists() {
            return Err(ResourceError::FileNotFound(path.to_string()));
        }
        
        let data = fs::read(path)
            .map_err(|e| ResourceError::IoError(e.to_string()))?;
        
        let font = FontResource {
            path: path.to_string(),
            data,
        };
        
        Ok(Arc::new(font))
    }
    
    fn save(&self, resource: &dyn Resource, path: &str) -> Result<(), ResourceError> {
        if let Some(font) = resource.as_any().downcast_ref::<FontResource>() {
            fs::write(path, &font.data)
                .map_err(|e| ResourceError::IoError(e.to_string()))?;
            Ok(())
        } else {
            Err(ResourceError::TypeMismatch {
                expected: "FontResource".to_string(),
                actual: resource.get_type_name().to_string(),
            })
        }
    }
    
    fn get_supported_extensions(&self) -> Vec<String> {
        vec!["ttf".to_string(), "otf".to_string(), "woff".to_string(), "woff2".to_string()]
    }
}

/// Shader resource loader
pub struct ShaderLoader;

impl ShaderLoader {
    pub fn new() -> Self {
        Self
    }
}

impl ResourceLoader for ShaderLoader {
    fn load(&self, path: &str) -> Result<Arc<dyn Resource>, ResourceError> {
        if !std::path::Path::new(path).exists() {
            return Err(ResourceError::FileNotFound(path.to_string()));
        }
        
        let content = fs::read_to_string(path)
            .map_err(|e| ResourceError::IoError(e.to_string()))?;
        
        let shader = ShaderResource {
            path: path.to_string(),
            source: content,
            shader_type: ShaderType::Fragment, // Default, would be determined from file
        };
        
        Ok(Arc::new(shader))
    }
    
    fn save(&self, resource: &dyn Resource, path: &str) -> Result<(), ResourceError> {
        if let Some(shader) = resource.as_any().downcast_ref::<ShaderResource>() {
            fs::write(path, &shader.source)
                .map_err(|e| ResourceError::IoError(e.to_string()))?;
            Ok(())
        } else {
            Err(ResourceError::TypeMismatch {
                expected: "ShaderResource".to_string(),
                actual: resource.get_type_name().to_string(),
            })
        }
    }
    
    fn get_supported_extensions(&self) -> Vec<String> {
        vec!["glsl".to_string(), "vert".to_string(), "frag".to_string(), "wgsl".to_string()]
    }
}

/// Resource loader registry
#[derive(Debug, Default)]
pub struct ResourceLoaderRegistry {
    loaders: HashMap<String, Box<dyn ResourceLoader>>,
}

impl ResourceLoaderRegistry {
    /// Create a new registry
    pub fn new() -> Self {
        Self {
            loaders: HashMap::new(),
        }
    }
    
    /// Register a loader for file extensions
    pub fn register(&mut self, extensions: Vec<String>, loader: Box<dyn ResourceLoader>) {
        for ext in extensions {
            self.loaders.insert(ext.to_lowercase(), loader.clone());
        }
    }
    
    /// Get a loader for a file extension
    pub fn get_loader(&self, extension: &str) -> Option<&dyn ResourceLoader> {
        self.loaders.get(&extension.to_lowercase()).map(|l| l.as_ref())
    }
    
    /// Check if an extension is supported
    pub fn is_supported(&self, extension: &str) -> bool {
        self.loaders.contains_key(&extension.to_lowercase())
    }
    
    /// Get all supported extensions
    pub fn get_supported_extensions(&self) -> Vec<String> {
        self.loaders.keys().cloned().collect()
    }
}

// Implement Clone for Box<dyn ResourceLoader> (needed for registry)
trait ClonableResourceLoader: ResourceLoader {
    fn clone_box(&self) -> Box<dyn ResourceLoader>;
}

impl<T> ClonableResourceLoader for T
where
    T: ResourceLoader + Clone + 'static,
{
    fn clone_box(&self) -> Box<dyn ResourceLoader> {
        Box::new(self.clone())
    }
}

impl Clone for Box<dyn ResourceLoader> {
    fn clone(&self) -> Self {
        // This is a simplified approach - in a real implementation,
        // you'd need a more sophisticated cloning mechanism
        Box::new(ImageLoader::new()) // Placeholder
    }
}
