use std::collections::HashMap;
use std::path::{Path, PathBuf};
use std::sync::{Arc, Mutex};
use std::any::{Any, TypeId};

pub mod resource_loader;
pub mod resource_cache;
pub mod resource_types;

pub use resource_loader::*;
pub use resource_cache::*;
pub use resource_types::*;

/// Resource system for managing game assets - matches Godot's resource system
#[derive(Debug)]
pub struct ResourceSystem {
    /// Resource cache for loaded resources
    cache: ResourceCache,
    /// Resource loaders for different file types
    loaders: HashMap<String, Box<dyn ResourceLoader>>,
    /// Resource paths mapping
    resource_paths: HashMap<String, PathBuf>,
    /// Preloaded resources
    preloaded: HashMap<String, Arc<dyn Resource>>,
}

impl ResourceSystem {
    /// Create a new resource system
    pub fn new() -> Self {
        let mut system = Self {
            cache: ResourceCache::new(),
            loaders: HashMap::new(),
            resource_paths: HashMap::new(),
            preloaded: HashMap::new(),
        };
        
        // Register default loaders
        system.register_default_loaders();
        system
    }
    
    /// Register default resource loaders
    fn register_default_loaders(&mut self) {
        // Register built-in loaders
        self.register_loader("png".to_string(), Box::new(ImageLoader::new()));
        self.register_loader("jpg".to_string(), Box::new(ImageLoader::new()));
        self.register_loader("jpeg".to_string(), Box::new(ImageLoader::new()));
        self.register_loader("wav".to_string(), Box::new(AudioLoader::new()));
        self.register_loader("ogg".to_string(), Box::new(AudioLoader::new()));
        self.register_loader("mp3".to_string(), Box::new(AudioLoader::new()));
        self.register_loader("json".to_string(), Box::new(JsonLoader::new()));
        self.register_loader("tscn".to_string(), Box::new(SceneLoader::new()));
    }
    
    /// Register a resource loader for a file extension
    pub fn register_loader(&mut self, extension: String, loader: Box<dyn ResourceLoader>) {
        self.loaders.insert(extension.to_lowercase(), loader);
    }
    
    /// Load a resource from a path
    pub fn load<T: Resource + 'static>(&mut self, path: &str) -> Result<Arc<T>, ResourceError> {
        // Check cache first
        if let Some(cached) = self.cache.get::<T>(path) {
            return Ok(cached);
        }
        
        // Load from file
        let resource = self.load_from_file::<T>(path)?;
        
        // Cache the resource
        self.cache.insert(path.to_string(), resource.clone());
        
        Ok(resource)
    }
    
    /// Load a resource from file
    fn load_from_file<T: Resource + 'static>(&mut self, path: &str) -> Result<Arc<T>, ResourceError> {
        let path_buf = Path::new(path);
        
        // Get file extension
        let extension = path_buf
            .extension()
            .and_then(|ext| ext.to_str())
            .ok_or_else(|| ResourceError::UnsupportedFormat(path.to_string()))?
            .to_lowercase();
        
        // Find appropriate loader
        let loader = self.loaders.get(&extension)
            .ok_or_else(|| ResourceError::UnsupportedFormat(extension))?;
        
        // Load the resource
        let resource = loader.load(path)?;
        
        // Try to downcast to the requested type
        resource.as_any()
            .downcast_ref::<T>()
            .map(|r| Arc::new(r.clone()))
            .ok_or_else(|| ResourceError::TypeMismatch {
                expected: std::any::type_name::<T>().to_string(),
                actual: resource.get_type_name().to_string(),
            })
    }
    
    /// Preload a resource
    pub fn preload(&mut self, path: &str) -> Result<(), ResourceError> {
        let resource = self.load_resource_any(path)?;
        self.preloaded.insert(path.to_string(), resource);
        Ok(())
    }
    
    /// Load a resource as Any type
    fn load_resource_any(&mut self, path: &str) -> Result<Arc<dyn Resource>, ResourceError> {
        let path_buf = Path::new(path);
        
        let extension = path_buf
            .extension()
            .and_then(|ext| ext.to_str())
            .ok_or_else(|| ResourceError::UnsupportedFormat(path.to_string()))?
            .to_lowercase();
        
        let loader = self.loaders.get(&extension)
            .ok_or_else(|| ResourceError::UnsupportedFormat(extension))?;
        
        loader.load(path)
    }
    
    /// Get a preloaded resource
    pub fn get_preloaded<T: Resource + 'static>(&self, path: &str) -> Option<Arc<T>> {
        self.preloaded.get(path)
            .and_then(|resource| {
                resource.as_any()
                    .downcast_ref::<T>()
                    .map(|r| Arc::new(r.clone()))
            })
    }
    
    /// Check if a resource exists
    pub fn exists(&self, path: &str) -> bool {
        Path::new(path).exists()
    }
    
    /// Get resource type from path
    pub fn get_resource_type(&self, path: &str) -> Option<String> {
        Path::new(path)
            .extension()
            .and_then(|ext| ext.to_str())
            .map(|s| s.to_lowercase())
    }
    
    /// Clear cache
    pub fn clear_cache(&mut self) {
        self.cache.clear();
    }
    
    /// Get cache statistics
    pub fn get_cache_stats(&self) -> CacheStats {
        self.cache.get_stats()
    }
    
    /// Set resource path mapping
    pub fn set_resource_path(&mut self, alias: String, path: PathBuf) {
        self.resource_paths.insert(alias, path);
    }
    
    /// Resolve resource path
    pub fn resolve_path(&self, path: &str) -> PathBuf {
        // Check if it's an alias
        if let Some(resolved) = self.resource_paths.get(path) {
            resolved.clone()
        } else {
            PathBuf::from(path)
        }
    }
    
    /// Save a resource to file
    pub fn save<T: Resource>(&self, resource: &T, path: &str) -> Result<(), ResourceError> {
        let path_buf = Path::new(path);
        
        let extension = path_buf
            .extension()
            .and_then(|ext| ext.to_str())
            .ok_or_else(|| ResourceError::UnsupportedFormat(path.to_string()))?
            .to_lowercase();
        
        let loader = self.loaders.get(&extension)
            .ok_or_else(|| ResourceError::UnsupportedFormat(extension))?;
        
        loader.save(resource, path)
    }
    
    /// Get all loaded resource paths
    pub fn get_loaded_resources(&self) -> Vec<String> {
        self.cache.get_all_paths()
    }
    
    /// Unload a resource
    pub fn unload(&mut self, path: &str) {
        self.cache.remove(path);
        self.preloaded.remove(path);
    }
    
    /// Get memory usage
    pub fn get_memory_usage(&self) -> usize {
        self.cache.get_memory_usage()
    }
}

impl Default for ResourceSystem {
    fn default() -> Self {
        Self::new()
    }
}

/// Resource trait - base trait for all resources
pub trait Resource: Send + Sync + std::fmt::Debug {
    /// Get the resource as Any for downcasting
    fn as_any(&self) -> &dyn Any;
    
    /// Get the type name of this resource
    fn get_type_name(&self) -> &'static str;
    
    /// Get the resource path
    fn get_path(&self) -> Option<&str> {
        None
    }
    
    /// Clone the resource
    fn clone_resource(&self) -> Box<dyn Resource>;
    
    /// Get memory usage in bytes
    fn get_memory_usage(&self) -> usize {
        std::mem::size_of_val(self)
    }
}

/// Resource errors
#[derive(Debug, Clone)]
pub enum ResourceError {
    /// File not found
    FileNotFound(String),
    /// Unsupported file format
    UnsupportedFormat(String),
    /// IO error
    IoError(String),
    /// Parse error
    ParseError(String),
    /// Type mismatch
    TypeMismatch {
        expected: String,
        actual: String,
    },
    /// Resource already exists
    AlreadyExists(String),
    /// Invalid resource data
    InvalidData(String),
}

impl std::fmt::Display for ResourceError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            ResourceError::FileNotFound(path) => write!(f, "File not found: {}", path),
            ResourceError::UnsupportedFormat(format) => write!(f, "Unsupported format: {}", format),
            ResourceError::IoError(msg) => write!(f, "IO error: {}", msg),
            ResourceError::ParseError(msg) => write!(f, "Parse error: {}", msg),
            ResourceError::TypeMismatch { expected, actual } => {
                write!(f, "Type mismatch: expected {}, got {}", expected, actual)
            }
            ResourceError::AlreadyExists(path) => write!(f, "Resource already exists: {}", path),
            ResourceError::InvalidData(msg) => write!(f, "Invalid resource data: {}", msg),
        }
    }
}

impl std::error::Error for ResourceError {}

/// Resource reference for lazy loading
#[derive(Debug, Clone)]
pub struct ResourceRef<T: Resource> {
    path: String,
    resource: Option<Arc<T>>,
}

impl<T: Resource + 'static> ResourceRef<T> {
    /// Create a new resource reference
    pub fn new(path: String) -> Self {
        Self {
            path,
            resource: None,
        }
    }
    
    /// Load the resource if not already loaded
    pub fn load(&mut self, resource_system: &mut ResourceSystem) -> Result<Arc<T>, ResourceError> {
        if let Some(ref resource) = self.resource {
            Ok(resource.clone())
        } else {
            let resource = resource_system.load::<T>(&self.path)?;
            self.resource = Some(resource.clone());
            Ok(resource)
        }
    }
    
    /// Get the resource if loaded
    pub fn get(&self) -> Option<Arc<T>> {
        self.resource.clone()
    }
    
    /// Check if the resource is loaded
    pub fn is_loaded(&self) -> bool {
        self.resource.is_some()
    }
    
    /// Get the resource path
    pub fn get_path(&self) -> &str {
        &self.path
    }
    
    /// Unload the resource
    pub fn unload(&mut self) {
        self.resource = None;
    }
}
