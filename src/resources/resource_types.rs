use super::*;
use std::any::Any;

/// Image resource
#[derive(Debu<PERSON>, <PERSON>lone)]
pub struct ImageResource {
    pub path: String,
    pub width: u32,
    pub height: u32,
    pub format: ImageFormat,
    pub data: Vec<u8>,
}

impl Resource for ImageResource {
    fn as_any(&self) -> &dyn Any {
        self
    }
    
    fn get_type_name(&self) -> &'static str {
        "ImageResource"
    }
    
    fn get_path(&self) -> Option<&str> {
        Some(&self.path)
    }
    
    fn clone_resource(&self) -> Box<dyn Resource> {
        Box::new(self.clone())
    }
    
    fn get_memory_usage(&self) -> usize {
        std::mem::size_of::<Self>() + self.data.len() + self.path.len()
    }
}

/// Image formats
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum ImageFormat {
    RGB8,
    RGBA8,
    RGB16,
    RGBA16,
    RGB32F,
    RGBA32F,
}

/// Audio resource
#[derive(Debug, <PERSON><PERSON>)]
pub struct AudioResource {
    pub path: String,
    pub sample_rate: u32,
    pub channels: u16,
    pub format: AudioFormat,
    pub data: Vec<u8>,
}

impl Resource for AudioResource {
    fn as_any(&self) -> &dyn Any {
        self
    }
    
    fn get_type_name(&self) -> &'static str {
        "AudioResource"
    }
    
    fn get_path(&self) -> Option<&str> {
        Some(&self.path)
    }
    
    fn clone_resource(&self) -> Box<dyn Resource> {
        Box::new(self.clone())
    }
    
    fn get_memory_usage(&self) -> usize {
        std::mem::size_of::<Self>() + self.data.len() + self.path.len()
    }
}

/// Audio formats
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum AudioFormat {
    U8,
    I16,
    I24,
    I32,
    F32,
}

/// JSON resource
#[derive(Debug, Clone)]
pub struct JsonResource {
    pub path: String,
    pub content: String,
}

impl Resource for JsonResource {
    fn as_any(&self) -> &dyn Any {
        self
    }
    
    fn get_type_name(&self) -> &'static str {
        "JsonResource"
    }
    
    fn get_path(&self) -> Option<&str> {
        Some(&self.path)
    }
    
    fn clone_resource(&self) -> Box<dyn Resource> {
        Box::new(self.clone())
    }
    
    fn get_memory_usage(&self) -> usize {
        std::mem::size_of::<Self>() + self.content.len() + self.path.len()
    }
}

/// Scene resource
#[derive(Debug, Clone)]
pub struct SceneResource {
    pub path: String,
    pub content: String,
}

impl Resource for SceneResource {
    fn as_any(&self) -> &dyn Any {
        self
    }
    
    fn get_type_name(&self) -> &'static str {
        "SceneResource"
    }
    
    fn get_path(&self) -> Option<&str> {
        Some(&self.path)
    }
    
    fn clone_resource(&self) -> Box<dyn Resource> {
        Box::new(self.clone())
    }
    
    fn get_memory_usage(&self) -> usize {
        std::mem::size_of::<Self>() + self.content.len() + self.path.len()
    }
}

/// Font resource
#[derive(Debug, Clone)]
pub struct FontResource {
    pub path: String,
    pub data: Vec<u8>,
}

impl Resource for FontResource {
    fn as_any(&self) -> &dyn Any {
        self
    }
    
    fn get_type_name(&self) -> &'static str {
        "FontResource"
    }
    
    fn get_path(&self) -> Option<&str> {
        Some(&self.path)
    }
    
    fn clone_resource(&self) -> Box<dyn Resource> {
        Box::new(self.clone())
    }
    
    fn get_memory_usage(&self) -> usize {
        std::mem::size_of::<Self>() + self.data.len() + self.path.len()
    }
}

/// Shader resource
#[derive(Debug, Clone)]
pub struct ShaderResource {
    pub path: String,
    pub source: String,
    pub shader_type: ShaderType,
}

impl Resource for ShaderResource {
    fn as_any(&self) -> &dyn Any {
        self
    }
    
    fn get_type_name(&self) -> &'static str {
        "ShaderResource"
    }
    
    fn get_path(&self) -> Option<&str> {
        Some(&self.path)
    }
    
    fn clone_resource(&self) -> Box<dyn Resource> {
        Box::new(self.clone())
    }
    
    fn get_memory_usage(&self) -> usize {
        std::mem::size_of::<Self>() + self.source.len() + self.path.len()
    }
}

/// Shader types
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum ShaderType {
    Vertex,
    Fragment,
    Compute,
    Geometry,
    TessellationControl,
    TessellationEvaluation,
}

/// Texture resource (processed image)
#[derive(Debug, Clone)]
pub struct TextureResource {
    pub path: String,
    pub image: ImageResource,
    pub filter: TextureFilter,
    pub wrap: TextureWrap,
    pub mipmaps: bool,
}

impl Resource for TextureResource {
    fn as_any(&self) -> &dyn Any {
        self
    }
    
    fn get_type_name(&self) -> &'static str {
        "TextureResource"
    }
    
    fn get_path(&self) -> Option<&str> {
        Some(&self.path)
    }
    
    fn clone_resource(&self) -> Box<dyn Resource> {
        Box::new(self.clone())
    }
    
    fn get_memory_usage(&self) -> usize {
        std::mem::size_of::<Self>() + self.image.get_memory_usage() + self.path.len()
    }
}

/// Texture filtering modes
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum TextureFilter {
    Nearest,
    Linear,
    NearestMipmapNearest,
    LinearMipmapNearest,
    NearestMipmapLinear,
    LinearMipmapLinear,
}

/// Texture wrap modes
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum TextureWrap {
    Repeat,
    MirroredRepeat,
    ClampToEdge,
    ClampToBorder,
}

/// Material resource
#[derive(Debug, Clone)]
pub struct MaterialResource {
    pub path: String,
    pub shader: Option<String>, // Path to shader resource
    pub properties: HashMap<String, MaterialProperty>,
}

impl Resource for MaterialResource {
    fn as_any(&self) -> &dyn Any {
        self
    }
    
    fn get_type_name(&self) -> &'static str {
        "MaterialResource"
    }
    
    fn get_path(&self) -> Option<&str> {
        Some(&self.path)
    }
    
    fn clone_resource(&self) -> Box<dyn Resource> {
        Box::new(self.clone())
    }
    
    fn get_memory_usage(&self) -> usize {
        std::mem::size_of::<Self>() + 
        self.path.len() + 
        self.shader.as_ref().map_or(0, |s| s.len()) +
        self.properties.len() * std::mem::size_of::<(String, MaterialProperty)>()
    }
}

/// Material property types
#[derive(Debug, Clone)]
pub enum MaterialProperty {
    Float(f32),
    Vec2([f32; 2]),
    Vec3([f32; 3]),
    Vec4([f32; 4]),
    Int(i32),
    Bool(bool),
    Texture(String), // Path to texture resource
}

/// Mesh resource
#[derive(Debug, Clone)]
pub struct MeshResource {
    pub path: String,
    pub vertices: Vec<MeshVertex>,
    pub indices: Vec<u32>,
    pub materials: Vec<String>, // Paths to material resources
}

impl Resource for MeshResource {
    fn as_any(&self) -> &dyn Any {
        self
    }
    
    fn get_type_name(&self) -> &'static str {
        "MeshResource"
    }
    
    fn get_path(&self) -> Option<&str> {
        Some(&self.path)
    }
    
    fn clone_resource(&self) -> Box<dyn Resource> {
        Box::new(self.clone())
    }
    
    fn get_memory_usage(&self) -> usize {
        std::mem::size_of::<Self>() + 
        self.path.len() +
        self.vertices.len() * std::mem::size_of::<MeshVertex>() +
        self.indices.len() * std::mem::size_of::<u32>() +
        self.materials.iter().map(|m| m.len()).sum::<usize>()
    }
}

/// Mesh vertex data
#[derive(Debug, Clone, Copy)]
pub struct MeshVertex {
    pub position: [f32; 3],
    pub normal: [f32; 3],
    pub uv: [f32; 2],
    pub color: [f32; 4],
}

/// Animation resource
#[derive(Debug, Clone)]
pub struct AnimationResourceData {
    pub path: String,
    pub animation: crate::animation::Animation,
}

impl Resource for AnimationResourceData {
    fn as_any(&self) -> &dyn Any {
        self
    }
    
    fn get_type_name(&self) -> &'static str {
        "AnimationResource"
    }
    
    fn get_path(&self) -> Option<&str> {
        Some(&self.path)
    }
    
    fn clone_resource(&self) -> Box<dyn Resource> {
        Box::new(self.clone())
    }
    
    fn get_memory_usage(&self) -> usize {
        std::mem::size_of::<Self>() + self.path.len()
        // + animation memory usage calculation
    }
}
