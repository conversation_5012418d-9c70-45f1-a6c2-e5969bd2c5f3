use crate::performance::PerformanceTracker;
use crate::physics::{PhysicsWorld, ObjectType};
use egui::{Context, Ui, Color32, Stroke};
use nalgebra::Vector2;

/// Debug UI state and configuration
pub struct DebugUI {
    pub show_performance: bool,
    pub show_physics_controls: bool,
    pub show_object_spawner: bool,
    pub show_simulation_controls: bool,
    pub show_help: bool,
    pub show_test_scenarios: bool,
    pub spawn_position: [f32; 2],
    pub spawn_size: f32,
    pub spawn_object_type: ObjectType,
    pub gravity: [f32; 2],
    pub paused: bool,
    pub step_simulation: bool,
    pub clear_objects: bool,
    pub performance_plot_height: f32,
}

impl DebugUI {
    /// Create a new debug UI
    pub fn new() -> Self {
        Self {
            show_performance: true,
            show_physics_controls: true,
            show_object_spawner: true,
            show_simulation_controls: true,
            show_help: false,
            show_test_scenarios: true,
            spawn_position: [0.0, 5.0],
            spawn_size: 1.0,
            spawn_object_type: ObjectType::Ball,
            gravity: [0.0, -9.81],
            paused: false,
            step_simulation: false,
            clear_objects: false,
            performance_plot_height: 100.0,
        }
    }

    /// Render the debug UI
    pub fn render(&mut self, ctx: &Context, physics_world: &mut PhysicsWorld, performance_tracker: &PerformanceTracker, test_scenario_manager: &mut crate::test_scenarios::TestScenarioManager) {
        // Main menu bar
        egui::TopBottomPanel::top("menu_bar").show(ctx, |ui| {
            egui::MenuBar::new().ui(ui, |ui| {
                ui.menu_button("View", |ui| {
                    ui.checkbox(&mut self.show_performance, "Performance");
                    ui.checkbox(&mut self.show_physics_controls, "Physics Controls");
                    ui.checkbox(&mut self.show_object_spawner, "Object Spawner");
                    ui.checkbox(&mut self.show_simulation_controls, "Simulation Controls");
                    ui.checkbox(&mut self.show_test_scenarios, "Test Scenarios");
                    ui.separator();
                    ui.checkbox(&mut self.show_help, "Help & Controls");
                });

                ui.separator();

                let metrics = performance_tracker.get_metrics(physics_world.active_body_count());
                ui.label(format!("FPS: {:.1}", metrics.fps));
                ui.label(format!("Objects: {}", metrics.physics_objects_count));
            });
        });

        // Performance panel
        if self.show_performance {
            egui::Window::new("Performance Metrics")
                .default_width(300.0)
                .show(ctx, |ui| {
                    self.render_performance_panel(ui, performance_tracker, physics_world);
                });
        }

        // Physics controls panel
        if self.show_physics_controls {
            egui::Window::new("Physics Controls")
                .default_width(250.0)
                .show(ctx, |ui| {
                    self.render_physics_controls_panel(ui, physics_world);
                });
        }

        // Object spawner panel
        if self.show_object_spawner {
            egui::Window::new("Object Spawner")
                .default_width(200.0)
                .show(ctx, |ui| {
                    self.render_object_spawner_panel(ui, physics_world);
                });
        }

        // Simulation controls panel
        if self.show_simulation_controls {
            egui::Window::new("Simulation Controls")
                .default_width(200.0)
                .show(ctx, |ui| {
                    self.render_simulation_controls_panel(ui, physics_world);
                });
        }

        // Test scenarios panel
        if self.show_test_scenarios {
            egui::Window::new("Test Scenarios")
                .default_width(300.0)
                .show(ctx, |ui| {
                    self.render_test_scenarios_panel(ui, physics_world, test_scenario_manager);
                });
        }

        // Help panel
        if self.show_help {
            egui::Window::new("Help & Controls")
                .default_width(400.0)
                .show(ctx, |ui| {
                    self.render_help_panel(ui);
                });
        }
    }

    /// Render the performance metrics panel
    fn render_performance_panel(&mut self, ui: &mut Ui, performance_tracker: &PerformanceTracker, physics_world: &PhysicsWorld) {
        let metrics = performance_tracker.get_metrics(physics_world.active_body_count());

        ui.heading("Performance Metrics");
        ui.separator();

        // Current metrics
        ui.label(format!("FPS: {:.1}", metrics.fps));
        ui.label(format!("Frame Time: {:.2} ms", metrics.frame_time_ms));
        ui.label(format!("Physics Time: {:.2} ms", metrics.physics_time_ms));
        ui.label(format!("Render Time: {:.2} ms", metrics.render_time_ms));
        ui.label(format!("Memory Usage: {:.1} MB", metrics.memory_usage_mb));

        ui.separator();

        // Performance statistics
        ui.label("Frame Time Statistics:");
        ui.label(format!("  Min: {:.2} ms", performance_tracker.min_frame_time()));
        ui.label(format!("  Max: {:.2} ms", performance_tracker.max_frame_time()));
        ui.label(format!("  95th: {:.2} ms", performance_tracker.frame_time_percentile(0.95)));
        ui.label(format!("  99th: {:.2} ms", performance_tracker.frame_time_percentile(0.99)));

        ui.separator();

        // Performance plot
        ui.label("Frame Time History:");
        ui.add(egui::Slider::new(&mut self.performance_plot_height, 50.0..=200.0).text("Plot Height"));
        
        let frame_times = performance_tracker.get_frame_time_samples();
        if !frame_times.is_empty() {
            self.render_performance_plot(ui, &frame_times, "Frame Time (ms)", Color32::BLUE);
        }

        let physics_times = performance_tracker.get_physics_time_samples();
        if !physics_times.is_empty() {
            self.render_performance_plot(ui, &physics_times, "Physics Time (ms)", Color32::RED);
        }
    }

    /// Render a simple performance plot
    fn render_performance_plot(&self, ui: &mut Ui, data: &[f32], label: &str, color: Color32) {
        if data.is_empty() {
            return;
        }

        let max_value = data.iter().fold(0.0f32, |acc, &x| acc.max(x));
        let min_value = data.iter().fold(f32::INFINITY, |acc, &x| acc.min(x));
        let range = max_value - min_value;

        ui.label(label);
        
        let (response, painter) = ui.allocate_painter(
            egui::Vec2::new(ui.available_width(), self.performance_plot_height),
            egui::Sense::hover(),
        );

        let rect = response.rect;
        let stroke = Stroke::new(1.0, color);

        // Draw background
        painter.rect_filled(rect, 0.0, Color32::from_gray(20));

        // Draw data points
        if data.len() > 1 && range > 0.0 {
            let points: Vec<egui::Pos2> = data
                .iter()
                .enumerate()
                .map(|(i, &value)| {
                    let x = rect.left() + (i as f32 / (data.len() - 1) as f32) * rect.width();
                    let y = rect.bottom() - ((value - min_value) / range) * rect.height();
                    egui::Pos2::new(x, y)
                })
                .collect();

            painter.add(egui::Shape::line(points, stroke));
        }

        // Draw labels
        painter.text(
            rect.left_top() + egui::Vec2::new(5.0, 5.0),
            egui::Align2::LEFT_TOP,
            format!("{:.2}", max_value),
            egui::FontId::monospace(10.0),
            Color32::WHITE,
        );

        painter.text(
            rect.left_bottom() + egui::Vec2::new(5.0, -15.0),
            egui::Align2::LEFT_BOTTOM,
            format!("{:.2}", min_value),
            egui::FontId::monospace(10.0),
            Color32::WHITE,
        );
    }

    /// Render the physics controls panel
    fn render_physics_controls_panel(&mut self, ui: &mut Ui, physics_world: &mut PhysicsWorld) {
        ui.heading("Physics Controls");
        ui.separator();

        // Gravity controls
        ui.label("Gravity:");
        ui.horizontal(|ui| {
            ui.label("X:");
            ui.add(egui::DragValue::new(&mut self.gravity[0]).speed(0.1));
            ui.label("Y:");
            ui.add(egui::DragValue::new(&mut self.gravity[1]).speed(0.1));
        });

        if ui.button("Apply Gravity").clicked() {
            physics_world.set_gravity(Vector2::new(self.gravity[0], self.gravity[1]));
        }

        ui.separator();

        // Physics world info
        ui.label("World Information:");
        ui.label(format!("Active Bodies: {}", physics_world.active_body_count()));
        ui.label(format!("Colliders: {}", physics_world.collider_count()));
        ui.label(format!("Simulation Steps: {}", physics_world.step_count));

        ui.separator();

        // Preset gravity buttons
        ui.label("Gravity Presets:");
        ui.horizontal(|ui| {
            if ui.button("Earth").clicked() {
                self.gravity = [0.0, -9.81];
                physics_world.set_gravity(Vector2::new(0.0, -9.81));
            }
            if ui.button("Moon").clicked() {
                self.gravity = [0.0, -1.62];
                physics_world.set_gravity(Vector2::new(0.0, -1.62));
            }
            if ui.button("Zero G").clicked() {
                self.gravity = [0.0, 0.0];
                physics_world.set_gravity(Vector2::new(0.0, 0.0));
            }
        });
    }

    /// Render the object spawner panel
    fn render_object_spawner_panel(&mut self, ui: &mut Ui, physics_world: &mut PhysicsWorld) {
        ui.heading("Object Spawner");
        ui.separator();

        // Object type selection
        ui.label("Object Type:");
        ui.horizontal(|ui| {
            ui.selectable_value(&mut self.spawn_object_type, ObjectType::Ball, "Ball");
            ui.selectable_value(&mut self.spawn_object_type, ObjectType::Box, "Box");
        });

        ui.separator();

        // Position controls
        ui.label("Spawn Position:");
        ui.horizontal(|ui| {
            ui.label("X:");
            ui.add(egui::DragValue::new(&mut self.spawn_position[0]).speed(0.1));
            ui.label("Y:");
            ui.add(egui::DragValue::new(&mut self.spawn_position[1]).speed(0.1));
        });

        // Size control
        ui.label("Size:");
        ui.add(egui::Slider::new(&mut self.spawn_size, 0.1..=3.0).text("Size"));

        ui.separator();

        // Spawn buttons
        if ui.button("Spawn Object").clicked() {
            let position = Vector2::new(self.spawn_position[0], self.spawn_position[1]);
            match self.spawn_object_type {
                ObjectType::Ball => {
                    physics_world.create_ball(position, self.spawn_size);
                }
                ObjectType::Box => {
                    let half_extents = Vector2::new(self.spawn_size, self.spawn_size);
                    physics_world.create_box(position, half_extents);
                }
                ObjectType::Ground => {} // Ground is not spawnable
            }
        }

        ui.horizontal(|ui| {
            if ui.button("Spawn 10").clicked() {
                for i in 0..10 {
                    let offset = Vector2::new((i as f32 - 5.0) * 0.5, i as f32 * 0.5);
                    let position = Vector2::new(self.spawn_position[0], self.spawn_position[1]) + offset;
                    match self.spawn_object_type {
                        ObjectType::Ball => {
                            physics_world.create_ball(position, self.spawn_size);
                        }
                        ObjectType::Box => {
                            let half_extents = Vector2::new(self.spawn_size, self.spawn_size);
                            physics_world.create_box(position, half_extents);
                        }
                        ObjectType::Ground => {} // Ground is not spawnable
                    }
                }
            }

            if ui.button("Random Spawn").clicked() {
                use std::f32::consts::PI;
                let angle = (physics_world.step_count as f32 * 0.1) % (2.0 * PI);
                let radius = 2.0;
                let position = Vector2::new(
                    self.spawn_position[0] + angle.cos() * radius,
                    self.spawn_position[1] + 8.0,
                );
                match self.spawn_object_type {
                    ObjectType::Ball => {
                        physics_world.create_ball(position, self.spawn_size);
                    }
                    ObjectType::Box => {
                        let half_extents = Vector2::new(self.spawn_size, self.spawn_size);
                        physics_world.create_box(position, half_extents);
                    }
                    ObjectType::Ground => {} // Ground is not spawnable
                }
            }
        });
    }

    /// Render the simulation controls panel
    fn render_simulation_controls_panel(&mut self, ui: &mut Ui, physics_world: &mut PhysicsWorld) {
        ui.heading("Simulation Controls");
        ui.separator();

        // Pause/Resume
        ui.horizontal(|ui| {
            if ui.button(if self.paused { "Resume" } else { "Pause" }).clicked() {
                self.paused = !self.paused;
            }

            if ui.button("Step").clicked() {
                self.step_simulation = true;
            }
        });

        ui.separator();

        // Clear objects
        if ui.button("Clear All Objects").clicked() {
            self.clear_objects = true;
        }

        if self.clear_objects {
            physics_world.clear_dynamic_objects();
            self.clear_objects = false;
        }

        ui.separator();

        // Simulation status
        ui.label(format!("Status: {}", if self.paused { "Paused" } else { "Running" }));
    }

    /// Render the help panel with keyboard shortcuts and controls
    fn render_help_panel(&mut self, ui: &mut Ui) {
        ui.heading("Help & Controls");
        ui.separator();

        ui.label("🖱️ Mouse Controls:");
        ui.label("  • Left Click: Spawn object at cursor position");
        ui.label("  • Mouse position determines spawn location");

        ui.separator();

        ui.label("⌨️ Keyboard Shortcuts:");
        ui.label("  • F1: Toggle this help panel");
        ui.label("  • ESC: Exit application");
        ui.label("  • SPACE: Pause/Resume physics simulation");
        ui.label("  • R: Clear all dynamic objects");
        ui.label("  • G: Toggle gravity on/off");
        ui.label("  • 1: Select ball spawning mode");
        ui.label("  • 2: Select box spawning mode");
        ui.label("  • +/=: Increase spawn object size");
        ui.label("  • -: Decrease spawn object size");

        ui.separator();

        ui.label("🎮 Simulation Features:");
        ui.label("  • Real-time physics simulation using Rapier2D");
        ui.label("  • Performance monitoring and metrics");
        ui.label("  • Interactive object spawning and manipulation");
        ui.label("  • Configurable gravity and physics parameters");
        ui.label("  • Comprehensive benchmarking suite");

        ui.separator();

        ui.label("📊 Performance Metrics:");
        ui.label("  • Frame time and FPS tracking");
        ui.label("  • Physics step timing");
        ui.label("  • Memory usage estimation");
        ui.label("  • Real-time performance plots");

        ui.separator();

        ui.label("🔧 Debug Tools:");
        ui.label("  • Object spawner with size control");
        ui.label("  • Physics parameter adjustment");
        ui.label("  • Simulation pause/step controls");
        ui.label("  • Gravity presets (Earth, Moon, Zero-G)");

        ui.separator();

        if ui.button("Close Help").clicked() {
            self.show_help = false;
        }
    }

    /// Render the test scenarios panel
    fn render_test_scenarios_panel(
        &mut self,
        ui: &mut Ui,
        physics_world: &mut PhysicsWorld,
        test_scenario_manager: &mut crate::test_scenarios::TestScenarioManager,
    ) {
        ui.heading("Test Scenarios");
        ui.separator();

        ui.label("Select a test scenario to run:");

        // Current scenario display
        ui.horizontal(|ui| {
            ui.label("Current:");
            ui.label(test_scenario_manager.current_scenario.name());
        });

        ui.separator();

        // Scenario selection buttons
        let scenarios = crate::test_scenarios::TestScenario::all();
        for scenario in scenarios {
            ui.horizontal(|ui| {
                if ui.button(scenario.name()).clicked() {
                    test_scenario_manager.run_scenario(scenario, physics_world);
                }
                ui.label(scenario.description());
            });
        }

        ui.separator();

        // Auto-run controls
        ui.checkbox(&mut test_scenario_manager.auto_run_next, "Auto-run scenarios");

        if test_scenario_manager.auto_run_next {
            ui.horizontal(|ui| {
                ui.label("Duration:");
                ui.add(egui::Slider::new(&mut test_scenario_manager.scenario_duration, 5.0..=120.0)
                    .suffix(" seconds"));
            });

            let remaining = test_scenario_manager.remaining_time();
            ui.label(format!("Time remaining: {:.1}s", remaining));

            if ui.button("Next Scenario").clicked() {
                test_scenario_manager.next_scenario(physics_world);
            }
        }

        ui.separator();

        // Quick actions
        ui.label("Quick Actions:");
        ui.horizontal(|ui| {
            if ui.button("Clear All").clicked() {
                test_scenario_manager.run_scenario(crate::test_scenarios::TestScenario::Empty, physics_world);
            }
            if ui.button("Basic Drop").clicked() {
                test_scenario_manager.run_scenario(crate::test_scenarios::TestScenario::BasicDrop, physics_world);
            }
            if ui.button("Stress Test").clicked() {
                test_scenario_manager.run_scenario(crate::test_scenarios::TestScenario::StressTest, physics_world);
            }
        });
    }

    /// Check if simulation should step
    pub fn should_step_simulation(&mut self) -> bool {
        if self.paused {
            if self.step_simulation {
                self.step_simulation = false;
                true
            } else {
                false
            }
        } else {
            true
        }
    }
}

impl Default for DebugUI {
    fn default() -> Self {
        Self::new()
    }
}
