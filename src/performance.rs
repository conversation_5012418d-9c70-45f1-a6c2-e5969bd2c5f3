use instant::Instant;
use std::collections::VecDeque;

/// Performance metrics for the application
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct PerformanceMetrics {
    pub frame_time_ms: f32,
    pub physics_time_ms: f32,
    pub render_time_ms: f32,
    pub fps: f32,
    pub physics_objects_count: usize,
    pub memory_usage_mb: f32,
}

/// Performance tracker that collects and analyzes performance data
pub struct PerformanceTracker {
    frame_times: VecDeque<f32>,
    physics_times: VecDeque<f32>,
    render_times: VecDeque<f32>,
    max_samples: usize,
    frame_start: Option<Instant>,
    physics_start: Option<Instant>,
    render_start: Option<Instant>,
    last_frame_time: Instant,
}

impl PerformanceTracker {
    /// Create a new performance tracker
    pub fn new(max_samples: usize) -> Self {
        Self {
            frame_times: VecDeque::with_capacity(max_samples),
            physics_times: VecDeque::with_capacity(max_samples),
            render_times: VecDeque::with_capacity(max_samples),
            max_samples,
            frame_start: None,
            physics_start: None,
            render_start: None,
            last_frame_time: Instant::now(),
        }
    }

    /// Start timing a frame
    pub fn start_frame(&mut self) {
        self.frame_start = Some(Instant::now());
    }

    /// End timing a frame and record the duration
    pub fn end_frame(&mut self) {
        if let Some(start) = self.frame_start.take() {
            let duration = start.elapsed().as_secs_f32() * 1000.0; // Convert to milliseconds
            self.add_frame_time(duration);
        }
    }

    /// Start timing physics step
    pub fn start_physics(&mut self) {
        self.physics_start = Some(Instant::now());
    }

    /// End timing physics step and record the duration
    pub fn end_physics(&mut self) {
        if let Some(start) = self.physics_start.take() {
            let duration = start.elapsed().as_secs_f32() * 1000.0; // Convert to milliseconds
            self.add_physics_time(duration);
        }
    }

    /// Start timing render
    pub fn start_render(&mut self) {
        self.render_start = Some(Instant::now());
    }

    /// End timing render and record the duration
    pub fn end_render(&mut self) {
        if let Some(start) = self.render_start.take() {
            let duration = start.elapsed().as_secs_f32() * 1000.0; // Convert to milliseconds
            self.add_render_time(duration);
        }
    }

    /// Add a frame time sample
    fn add_frame_time(&mut self, time_ms: f32) {
        if self.frame_times.len() >= self.max_samples {
            self.frame_times.pop_front();
        }
        self.frame_times.push_back(time_ms);
    }

    /// Add a physics time sample
    fn add_physics_time(&mut self, time_ms: f32) {
        if self.physics_times.len() >= self.max_samples {
            self.physics_times.pop_front();
        }
        self.physics_times.push_back(time_ms);
    }

    /// Add a render time sample
    fn add_render_time(&mut self, time_ms: f32) {
        if self.render_times.len() >= self.max_samples {
            self.render_times.pop_front();
        }
        self.render_times.push_back(time_ms);
    }

    /// Get current performance metrics
    pub fn get_metrics(&self, physics_objects_count: usize) -> PerformanceMetrics {
        let frame_time_ms = self.average_frame_time();
        let physics_time_ms = self.average_physics_time();
        let render_time_ms = self.average_render_time();
        let fps = if frame_time_ms > 0.0 { 1000.0 / frame_time_ms } else { 0.0 };
        let memory_usage_mb = self.estimate_memory_usage();

        PerformanceMetrics {
            frame_time_ms,
            physics_time_ms,
            render_time_ms,
            fps,
            physics_objects_count,
            memory_usage_mb,
        }
    }

    /// Get average frame time in milliseconds
    pub fn average_frame_time(&self) -> f32 {
        if self.frame_times.is_empty() {
            0.0
        } else {
            self.frame_times.iter().sum::<f32>() / self.frame_times.len() as f32
        }
    }

    /// Get average physics time in milliseconds
    pub fn average_physics_time(&self) -> f32 {
        if self.physics_times.is_empty() {
            0.0
        } else {
            self.physics_times.iter().sum::<f32>() / self.physics_times.len() as f32
        }
    }

    /// Get average render time in milliseconds
    pub fn average_render_time(&self) -> f32 {
        if self.render_times.is_empty() {
            0.0
        } else {
            self.render_times.iter().sum::<f32>() / self.render_times.len() as f32
        }
    }

    /// Get minimum frame time in milliseconds
    pub fn min_frame_time(&self) -> f32 {
        self.frame_times.iter().copied().fold(f32::INFINITY, f32::min)
    }

    /// Get maximum frame time in milliseconds
    pub fn max_frame_time(&self) -> f32 {
        self.frame_times.iter().copied().fold(0.0, f32::max)
    }

    /// Get frame time percentile (0.0 to 1.0)
    pub fn frame_time_percentile(&self, percentile: f32) -> f32 {
        if self.frame_times.is_empty() {
            return 0.0;
        }

        let mut sorted_times: Vec<f32> = self.frame_times.iter().copied().collect();
        sorted_times.sort_by(|a, b| a.partial_cmp(b).unwrap());

        let index = ((sorted_times.len() - 1) as f32 * percentile.clamp(0.0, 1.0)) as usize;
        sorted_times[index]
    }

    /// Get frame time samples for plotting
    pub fn get_frame_time_samples(&self) -> Vec<f32> {
        self.frame_times.iter().copied().collect()
    }

    /// Get physics time samples for plotting
    pub fn get_physics_time_samples(&self) -> Vec<f32> {
        self.physics_times.iter().copied().collect()
    }

    /// Get render time samples for plotting
    pub fn get_render_time_samples(&self) -> Vec<f32> {
        self.render_times.iter().copied().collect()
    }

    /// Estimate memory usage (simplified)
    fn estimate_memory_usage(&self) -> f32 {
        // This is a simplified estimation
        // In a real application, you might use system APIs to get actual memory usage
        let samples_memory = (self.frame_times.len() + self.physics_times.len() + self.render_times.len()) * std::mem::size_of::<f32>();
        samples_memory as f32 / (1024.0 * 1024.0) // Convert to MB
    }

    /// Clear all performance data
    pub fn clear(&mut self) {
        self.frame_times.clear();
        self.physics_times.clear();
        self.render_times.clear();
    }

    /// Check if performance is degraded
    pub fn is_performance_degraded(&self, target_fps: f32) -> bool {
        let current_fps = if self.average_frame_time() > 0.0 {
            1000.0 / self.average_frame_time()
        } else {
            0.0
        };
        current_fps < target_fps * 0.8 // Consider degraded if below 80% of target
    }

    /// Get performance summary as a string
    pub fn get_summary(&self, physics_objects_count: usize) -> String {
        let metrics = self.get_metrics(physics_objects_count);
        format!(
            "FPS: {:.1} | Frame: {:.2}ms | Physics: {:.2}ms | Render: {:.2}ms | Objects: {} | Memory: {:.1}MB",
            metrics.fps,
            metrics.frame_time_ms,
            metrics.physics_time_ms,
            metrics.render_time_ms,
            metrics.physics_objects_count,
            metrics.memory_usage_mb
        )
    }
}

impl Default for PerformanceTracker {
    fn default() -> Self {
        Self::new(120) // Default to 2 seconds of samples at 60 FPS
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::thread;
    use std::time::Duration;

    #[test]
    fn test_performance_tracker_creation() {
        let tracker = PerformanceTracker::new(60);
        assert_eq!(tracker.max_samples, 60);
        assert_eq!(tracker.average_frame_time(), 0.0);
    }

    #[test]
    fn test_frame_timing() {
        let mut tracker = PerformanceTracker::new(10);
        
        tracker.start_frame();
        thread::sleep(Duration::from_millis(1));
        tracker.end_frame();
        
        assert!(tracker.average_frame_time() > 0.0);
    }

    #[test]
    fn test_physics_timing() {
        let mut tracker = PerformanceTracker::new(10);
        
        tracker.start_physics();
        thread::sleep(Duration::from_millis(1));
        tracker.end_physics();
        
        assert!(tracker.average_physics_time() > 0.0);
    }

    #[test]
    fn test_metrics_generation() {
        let mut tracker = PerformanceTracker::new(10);
        
        tracker.start_frame();
        thread::sleep(Duration::from_millis(1));
        tracker.end_frame();
        
        let metrics = tracker.get_metrics(5);
        assert!(metrics.frame_time_ms > 0.0);
        assert_eq!(metrics.physics_objects_count, 5);
    }

    #[test]
    fn test_sample_limit() {
        let mut tracker = PerformanceTracker::new(2);
        
        // Add more samples than the limit
        tracker.add_frame_time(1.0);
        tracker.add_frame_time(2.0);
        tracker.add_frame_time(3.0);
        
        assert_eq!(tracker.frame_times.len(), 2);
        assert_eq!(tracker.frame_times[0], 2.0);
        assert_eq!(tracker.frame_times[1], 3.0);
    }
}
