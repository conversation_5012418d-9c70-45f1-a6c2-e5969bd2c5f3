use super::*;

/// Blend modes for canvas items
#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Eq)]
pub enum BlendMode {
    Mix,
    Add,
    Sub,
    Mul,
    PremultAlpha,
    Disabled,
}

impl Default for BlendMode {
    fn default() -> Self {
        BlendMode::Mix
    }
}

/// Canvas item trait - matches <PERSON><PERSON>'s CanvasItem class
/// This is the base class for all 2D nodes that can be drawn
pub trait CanvasItem: Node {
    /// Get the z_index for rendering order
    fn z_index(&self) -> i32;
    
    /// Set the z_index for rendering order
    fn set_z_index(&mut self, z_index: i32);
    
    /// Check if z_index is relative to parent
    fn is_z_relative(&self) -> bool;
    
    /// Set if z_index is relative to parent
    fn set_z_as_relative(&mut self, relative: bool);
    
    /// Get visibility
    fn is_visible(&self) -> bool;
    
    /// Set visibility
    fn set_visible(&mut self, visible: bool);
    
    /// Get modulate color
    fn modulate(&self) -> [f32; 4];
    
    /// Set modulate color
    fn set_modulate(&mut self, color: [f32; 4]);
    
    /// Get self modulate color
    fn self_modulate(&self) -> [f32; 4];
    
    /// Set self modulate color
    fn set_self_modulate(&mut self, color: [f32; 4]);
    
    /// Get blend mode
    fn blend_mode(&self) -> BlendMode;
    
    /// Set blend mode
    fn set_blend_mode(&mut self, mode: BlendMode);
    
    /// Check if this item should be drawn
    fn should_draw(&self) -> bool {
        self.is_visible() && self.is_inside_tree()
    }
    
    /// Get the global modulate (combined with parent modulates)
    fn get_global_modulate(&self, scene: &Scene) -> [f32; 4] {
        let mut result = self.modulate();
        
        if let Some(parent_id) = self.parent() {
            if let Some(parent) = scene.get_node(parent_id) {
                // Try to cast parent to CanvasItem
                // In a real implementation, we'd use proper downcasting
                // For now, we'll just return the local modulate
            }
        }
        
        // Combine with self modulate
        let self_mod = self.self_modulate();
        result[0] *= self_mod[0];
        result[1] *= self_mod[1];
        result[2] *= self_mod[2];
        result[3] *= self_mod[3];
        
        result
    }
    
    /// Called when the item should be drawn
    fn _draw(&self, _render_context: &RenderContext) {}
}

/// Base implementation of CanvasItem
#[derive(Debug)]
pub struct BaseCanvasItem {
    base: BaseNode,
    z_index: i32,
    z_relative: bool,
    visible: bool,
    modulate: [f32; 4],
    self_modulate: [f32; 4],
    blend_mode: BlendMode,
}

impl BaseCanvasItem {
    /// Create a new base canvas item
    pub fn new(name: String) -> Self {
        Self {
            base: BaseNode::new(name),
            z_index: 0,
            z_relative: true,
            visible: true,
            modulate: [1.0, 1.0, 1.0, 1.0],
            self_modulate: [1.0, 1.0, 1.0, 1.0],
            blend_mode: BlendMode::default(),
        }
    }
    
    /// Get the base node
    pub fn base(&self) -> &BaseNode {
        &self.base
    }
    
    /// Get the base node mutably
    pub fn base_mut(&mut self) -> &mut BaseNode {
        &mut self.base
    }
}

impl Node for BaseCanvasItem {
    fn id(&self) -> NodeId {
        self.base.id()
    }
    
    fn name(&self) -> &str {
        self.base.name()
    }
    
    fn set_name(&mut self, name: String) {
        self.base.set_name(name);
    }
    
    fn parent(&self) -> Option<NodeId> {
        self.base.parent()
    }
    
    fn children(&self) -> &[NodeId] {
        self.base.children()
    }
    
    fn process_mode(&self) -> ProcessMode {
        self.base.process_mode()
    }
    
    fn set_process_mode(&mut self, mode: ProcessMode) {
        self.base.set_process_mode(mode);
    }
    
    fn is_inside_tree(&self) -> bool {
        self.base.is_inside_tree()
    }
    
    fn is_node_ready(&self) -> bool {
        self.base.is_node_ready()
    }
    
    fn get_groups(&self) -> &[String] {
        self.base.get_groups()
    }
    
    fn add_to_group(&mut self, group: String) {
        self.base.add_to_group(group);
    }
    
    fn remove_from_group(&mut self, group: &str) {
        self.base.remove_from_group(group);
    }
    
    fn is_in_group(&self, group: &str) -> bool {
        self.base.is_in_group(group)
    }
    
    fn add_child(&mut self, child_id: NodeId) {
        self.base.add_child(child_id);
    }
    
    fn remove_child(&mut self, child_id: NodeId) {
        self.base.remove_child(child_id);
    }
    
    fn set_parent(&mut self, parent_id: Option<NodeId>) {
        self.base.set_parent(parent_id);
    }
    
    fn get_path(&self, scene: &Scene) -> String {
        self.base.get_path(scene)
    }
    
    fn _enter_tree(&mut self, scene: &mut Scene) {
        self.base._enter_tree(scene);
    }
    
    fn _ready(&mut self, scene: &mut Scene) {
        self.base._ready(scene);
    }
    
    fn _exit_tree(&mut self, scene: &mut Scene) {
        self.base._exit_tree(scene);
    }
    
    fn _notification(&mut self, what: Notification, scene: &mut Scene) {
        self.base._notification(what, scene);

        match what {
            Notification::Draw => {
                // Handle drawing notification
            }
            Notification::VisibilityChanged => {
                // Handle visibility change
            }
            _ => {}
        }
    }

    fn as_any(&self) -> &dyn std::any::Any {
        self
    }

    fn as_any_mut(&mut self) -> &mut dyn std::any::Any {
        self
    }
}

impl CanvasItem for BaseCanvasItem {
    fn z_index(&self) -> i32 {
        self.z_index
    }
    
    fn set_z_index(&mut self, z_index: i32) {
        if self.z_index != z_index {
            self.z_index = z_index;
            // In a real implementation, we'd send a notification here
        }
    }
    
    fn is_z_relative(&self) -> bool {
        self.z_relative
    }
    
    fn set_z_as_relative(&mut self, relative: bool) {
        self.z_relative = relative;
    }
    
    fn is_visible(&self) -> bool {
        self.visible
    }
    
    fn set_visible(&mut self, visible: bool) {
        if self.visible != visible {
            self.visible = visible;
            // In a real implementation, we'd send a visibility changed notification
        }
    }
    
    fn modulate(&self) -> [f32; 4] {
        self.modulate
    }
    
    fn set_modulate(&mut self, color: [f32; 4]) {
        self.modulate = color;
    }
    
    fn self_modulate(&self) -> [f32; 4] {
        self.self_modulate
    }
    
    fn set_self_modulate(&mut self, color: [f32; 4]) {
        self.self_modulate = color;
    }
    
    fn blend_mode(&self) -> BlendMode {
        self.blend_mode
    }
    
    fn set_blend_mode(&mut self, mode: BlendMode) {
        self.blend_mode = mode;
    }
}

/// Context passed to nodes during rendering
pub struct RenderContext<'a> {
    pub device: &'a wgpu::Device,
    pub queue: &'a wgpu::Queue,
    pub render_pass: &'a mut wgpu::RenderPass<'a>,
    pub surface_config: &'a wgpu::SurfaceConfiguration,
}
