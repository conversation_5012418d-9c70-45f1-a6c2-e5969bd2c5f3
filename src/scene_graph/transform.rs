use nalgebra::{Matrix3, Matrix4, Vector2, Vector3};

/// 2D Transform for scene graph nodes
#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)]
pub struct Transform2D {
    /// Position in 2D space
    pub position: Vector2<f32>,
    /// Rotation in radians
    pub rotation: f32,
    /// Scale factor
    pub scale: Vector2<f32>,
}

impl Default for Transform2D {
    fn default() -> Self {
        Self {
            position: Vector2::new(0.0, 0.0),
            rotation: 0.0,
            scale: Vector2::new(1.0, 1.0),
        }
    }
}

impl Transform2D {
    /// Create a new transform with default values
    pub fn new() -> Self {
        Self::default()
    }
    
    /// Create a transform with the given position
    pub fn with_position(position: Vector2<f32>) -> Self {
        Self {
            position,
            ..Default::default()
        }
    }
    
    /// Create a transform with the given rotation
    pub fn with_rotation(rotation: f32) -> Self {
        Self {
            rotation,
            ..Default::default()
        }
    }
    
    /// Create a transform with the given scale
    pub fn with_scale(scale: Vector2<f32>) -> Self {
        Self {
            scale,
            ..Default::default()
        }
    }
    
    /// Set the position
    pub fn set_position(&mut self, position: Vector2<f32>) {
        self.position = position;
    }
    
    /// Set the rotation
    pub fn set_rotation(&mut self, rotation: f32) {
        self.rotation = rotation;
    }
    
    /// Set the scale
    pub fn set_scale(&mut self, scale: Vector2<f32>) {
        self.scale = scale;
    }
    
    /// Translate by the given offset
    pub fn translate(&mut self, offset: Vector2<f32>) {
        self.position += offset;
    }
    
    /// Rotate by the given angle
    pub fn rotate(&mut self, angle: f32) {
        self.rotation += angle;
    }
    
    /// Scale by the given factor
    pub fn scale_by(&mut self, factor: Vector2<f32>) {
        self.scale.component_mul_assign(&factor);
    }
    
    /// Convert to a 3x3 transformation matrix
    pub fn to_matrix3(&self) -> Matrix3<f32> {
        let cos_r = self.rotation.cos();
        let sin_r = self.rotation.sin();
        
        Matrix3::new(
            self.scale.x * cos_r, -self.scale.x * sin_r, self.position.x,
            self.scale.y * sin_r,  self.scale.y * cos_r, self.position.y,
            0.0,                   0.0,                  1.0,
        )
    }
    
    /// Convert to a 4x4 transformation matrix for GPU use
    pub fn to_matrix4(&self) -> Matrix4<f32> {
        let cos_r = self.rotation.cos();
        let sin_r = self.rotation.sin();
        
        Matrix4::new(
            self.scale.x * cos_r, -self.scale.x * sin_r, 0.0, self.position.x,
            self.scale.y * sin_r,  self.scale.y * cos_r, 0.0, self.position.y,
            0.0,                   0.0,                  1.0, 0.0,
            0.0,                   0.0,                  0.0, 1.0,
        )
    }
    
    /// Combine this transform with another (this * other)
    pub fn combine(&self, other: &Transform2D) -> Transform2D {
        let matrix_self = self.to_matrix3();
        let matrix_other = other.to_matrix3();
        let combined = matrix_self * matrix_other;
        
        // Extract position, rotation, and scale from the combined matrix
        let position = Vector2::new(combined[(0, 2)], combined[(1, 2)]);
        
        // Extract rotation from the rotation part of the matrix
        let rotation = combined[(1, 0)].atan2(combined[(0, 0)]);
        
        // Extract scale from the matrix
        let scale_x = (combined[(0, 0)].powi(2) + combined[(1, 0)].powi(2)).sqrt();
        let scale_y = (combined[(0, 1)].powi(2) + combined[(1, 1)].powi(2)).sqrt();
        let scale = Vector2::new(scale_x, scale_y);
        
        Transform2D {
            position,
            rotation,
            scale,
        }
    }
    
    /// Transform a point by this transform
    pub fn transform_point(&self, point: Vector2<f32>) -> Vector2<f32> {
        let matrix = self.to_matrix3();
        let homogeneous = Vector3::new(point.x, point.y, 1.0);
        let transformed = matrix * homogeneous;
        Vector2::new(transformed.x, transformed.y)
    }
    
    /// Transform a vector by this transform (ignoring translation)
    pub fn transform_vector(&self, vector: Vector2<f32>) -> Vector2<f32> {
        let cos_r = self.rotation.cos();
        let sin_r = self.rotation.sin();
        
        Vector2::new(
            self.scale.x * (cos_r * vector.x - sin_r * vector.y),
            self.scale.y * (sin_r * vector.x + cos_r * vector.y),
        )
    }
    
    /// Get the inverse of this transform
    pub fn inverse(&self) -> Transform2D {
        let inv_scale = Vector2::new(1.0 / self.scale.x, 1.0 / self.scale.y);
        let inv_rotation = -self.rotation;
        
        // Apply inverse rotation and scale to position
        let cos_r = inv_rotation.cos();
        let sin_r = inv_rotation.sin();
        let inv_position = Vector2::new(
            inv_scale.x * (cos_r * (-self.position.x) - sin_r * (-self.position.y)),
            inv_scale.y * (sin_r * (-self.position.x) + cos_r * (-self.position.y)),
        );
        
        Transform2D {
            position: inv_position,
            rotation: inv_rotation,
            scale: inv_scale,
        }
    }
    
    /// Convert screen coordinates to normalized device coordinates
    pub fn screen_to_ndc(screen_pos: Vector2<f32>, screen_size: Vector2<f32>) -> Vector2<f32> {
        Vector2::new(
            (screen_pos.x / screen_size.x) * 2.0 - 1.0,
            1.0 - (screen_pos.y / screen_size.y) * 2.0,
        )
    }
    
    /// Convert normalized device coordinates to screen coordinates
    pub fn ndc_to_screen(ndc_pos: Vector2<f32>, screen_size: Vector2<f32>) -> Vector2<f32> {
        Vector2::new(
            (ndc_pos.x + 1.0) * 0.5 * screen_size.x,
            (1.0 - ndc_pos.y) * 0.5 * screen_size.y,
        )
    }
}
