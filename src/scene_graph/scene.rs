use super::*;
use std::collections::HashMap;

/// Scene that manages a hierarchy of nodes
pub struct Scene {
    /// All nodes in the scene, indexed by their ID
    nodes: HashMap<NodeId, Box<dyn Node>>,
    /// Root node of the scene
    root_id: Option<NodeId>,
    /// Name of the scene
    name: String,
}

impl Scene {
    /// Create a new empty scene
    pub fn new(name: String) -> Self {
        Self {
            nodes: HashMap::new(),
            root_id: None,
            name,
        }
    }
    
    /// Get the name of the scene
    pub fn name(&self) -> &str {
        &self.name
    }
    
    /// Set the name of the scene
    pub fn set_name(&mut self, name: String) {
        self.name = name;
    }
    
    /// Get the root node ID
    pub fn root_id(&self) -> Option<NodeId> {
        self.root_id
    }
    
    /// Set the root node
    pub fn set_root(&mut self, node_id: NodeId) -> Result<(), SceneError> {
        if !self.nodes.contains_key(&node_id) {
            return Err(SceneError::NodeNotFound(node_id));
        }
        self.root_id = Some(node_id);
        Ok(())
    }
    
    /// Add a node to the scene
    pub fn add_node(&mut self, mut node: Box<dyn Node>) -> NodeId {
        let node_id = node.id();
        node._enter_tree(self);
        self.nodes.insert(node_id, node);

        // If this is the first node, make it the root
        if self.root_id.is_none() {
            self.root_id = Some(node_id);
        }

        node_id
    }
    
    /// Remove a node from the scene
    pub fn remove_node(&mut self, node_id: NodeId) -> Result<Box<dyn Node>, SceneError> {
        // First, collect the parent and children information
        let (parent_id, children) = if let Some(node) = self.nodes.get(&node_id) {
            (node.parent(), node.children().to_vec())
        } else {
            return Err(SceneError::NodeNotFound(node_id));
        };

        // Remove from parent's children list
        if let Some(parent_id) = parent_id {
            if let Some(parent) = self.nodes.get_mut(&parent_id) {
                parent.remove_child(node_id);
            }
        }

        // Remove this node as parent from all children
        for child_id in children {
            if let Some(child) = self.nodes.get_mut(&child_id) {
                child.set_parent(None);
            }
        }

        // Remove the node
        let mut node = self.nodes.remove(&node_id).ok_or(SceneError::NodeNotFound(node_id))?;
        node._exit_tree(self);

        // If this was the root, clear the root
        if self.root_id == Some(node_id) {
            self.root_id = None;
        }

        Ok(node)
    }
    
    /// Get a reference to a node
    pub fn get_node(&self, node_id: NodeId) -> Option<&dyn Node> {
        self.nodes.get(&node_id).map(|n| n.as_ref())
    }
    
    /// Get a mutable reference to a node
    pub fn get_node_mut(&mut self, node_id: NodeId) -> Option<&mut Box<dyn Node>> {
        self.nodes.get_mut(&node_id)
    }
    
    /// Add a child node to a parent node
    pub fn add_child(&mut self, parent_id: NodeId, child_id: NodeId) -> Result<(), SceneError> {
        // Check that both nodes exist
        if !self.nodes.contains_key(&parent_id) {
            return Err(SceneError::NodeNotFound(parent_id));
        }
        if !self.nodes.contains_key(&child_id) {
            return Err(SceneError::NodeNotFound(child_id));
        }
        
        // Remove child from its current parent if it has one
        if let Some(child) = self.nodes.get(&child_id) {
            if let Some(old_parent_id) = child.parent() {
                if let Some(old_parent) = self.nodes.get_mut(&old_parent_id) {
                    old_parent.remove_child(child_id);
                }
            }
        }
        
        // Set the new parent-child relationship
        if let Some(parent) = self.nodes.get_mut(&parent_id) {
            parent.add_child(child_id);
        }
        if let Some(child) = self.nodes.get_mut(&child_id) {
            child.set_parent(Some(parent_id));
        }
        
        Ok(())
    }
    
    /// Remove a child from its parent
    pub fn remove_child(&mut self, parent_id: NodeId, child_id: NodeId) -> Result<(), SceneError> {
        if let Some(parent) = self.nodes.get_mut(&parent_id) {
            parent.remove_child(child_id);
        }
        if let Some(child) = self.nodes.get_mut(&child_id) {
            child.set_parent(None);
        }
        Ok(())
    }
    
    /// Update all nodes in the scene
    pub fn update(&mut self, _delta_time: f32) {
        // Collect all node IDs to avoid borrowing issues
        let node_ids: Vec<NodeId> = self.nodes.keys().copied().collect();
        
        for node_id in node_ids {
            // We need to be careful about borrowing here
            // This is a simplified approach - in a real implementation,
            // we might want to use a more sophisticated system
            if self.nodes.contains_key(&node_id) {
                // For now, we'll skip the update to avoid borrowing issues
                // In a real implementation, we'd use interior mutability or
                // a different architecture
            }
        }
    }
    
    /// Traverse the scene tree and collect all renderable nodes
    pub fn collect_renderable_nodes(&self) -> Vec<(NodeId, &dyn Node)> {
        let mut renderable_nodes = Vec::new();

        if let Some(root_id) = self.root_id {
            self.collect_renderable_recursive(root_id, &mut renderable_nodes);
        }

        renderable_nodes
    }

    /// Recursively collect renderable nodes
    fn collect_renderable_recursive<'a>(&'a self, node_id: NodeId, renderable_nodes: &mut Vec<(NodeId, &'a dyn Node)>) {
        if let Some(node) = self.get_node(node_id) {
            // For now, we'll assume all nodes are potentially renderable
            // In a real implementation, we'd check if the node implements CanvasItem
            renderable_nodes.push((node_id, node));

            // Traverse children
            for &child_id in node.children() {
                self.collect_renderable_recursive(child_id, renderable_nodes);
            }
        }
    }
    
    /// Get all nodes in the scene
    pub fn get_all_nodes(&self) -> Vec<(NodeId, &dyn Node)> {
        self.nodes.iter().map(|(&id, node)| (id, node.as_ref())).collect()
    }
    
    /// Get the number of nodes in the scene
    pub fn node_count(&self) -> usize {
        self.nodes.len()
    }
    
    /// Check if the scene is empty
    pub fn is_empty(&self) -> bool {
        self.nodes.is_empty()
    }
    
    /// Clear all nodes from the scene
    pub fn clear(&mut self) {
        self.nodes.clear();
        self.root_id = None;
    }
}

/// Errors that can occur when working with scenes
#[derive(Debug, Clone)]
pub enum SceneError {
    /// Node with the given ID was not found
    NodeNotFound(NodeId),
    /// Attempted to create a circular reference in the scene graph
    CircularReference,
    /// Scene is in an invalid state
    InvalidState(String),
}

impl std::fmt::Display for SceneError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            SceneError::NodeNotFound(id) => write!(f, "Node with ID {:?} not found", id),
            SceneError::CircularReference => write!(f, "Circular reference detected in scene graph"),
            SceneError::InvalidState(msg) => write!(f, "Invalid scene state: {}", msg),
        }
    }
}

impl std::error::Error for SceneError {}

/// Scene manager that handles multiple scenes
pub struct SceneManager {
    scenes: HashMap<String, Scene>,
    active_scene: Option<String>,
}

impl SceneManager {
    /// Create a new scene manager
    pub fn new() -> Self {
        Self {
            scenes: HashMap::new(),
            active_scene: None,
        }
    }
    
    /// Add a scene to the manager
    pub fn add_scene(&mut self, scene: Scene) {
        let name = scene.name().to_string();
        self.scenes.insert(name.clone(), scene);
        
        // If this is the first scene, make it active
        if self.active_scene.is_none() {
            self.active_scene = Some(name);
        }
    }
    
    /// Remove a scene from the manager
    pub fn remove_scene(&mut self, name: &str) -> Option<Scene> {
        let scene = self.scenes.remove(name);
        
        // If we removed the active scene, clear the active scene
        if self.active_scene.as_ref() == Some(&name.to_string()) {
            self.active_scene = None;
        }
        
        scene
    }
    
    /// Set the active scene
    pub fn set_active_scene(&mut self, name: &str) -> Result<(), SceneError> {
        if !self.scenes.contains_key(name) {
            return Err(SceneError::InvalidState(format!("Scene '{}' not found", name)));
        }
        self.active_scene = Some(name.to_string());
        Ok(())
    }
    
    /// Get the active scene
    pub fn get_active_scene(&self) -> Option<&Scene> {
        self.active_scene.as_ref().and_then(|name| self.scenes.get(name))
    }
    
    /// Get the active scene mutably
    pub fn get_active_scene_mut(&mut self) -> Option<&mut Scene> {
        self.active_scene.clone().and_then(move |name| self.scenes.get_mut(&name))
    }
    
    /// Get a scene by name
    pub fn get_scene(&self, name: &str) -> Option<&Scene> {
        self.scenes.get(name)
    }
    
    /// Get a scene by name mutably
    pub fn get_scene_mut(&mut self, name: &str) -> Option<&mut Scene> {
        self.scenes.get_mut(name)
    }
    
    /// Update the active scene
    pub fn update(&mut self, delta_time: f32) {
        if let Some(scene) = self.get_active_scene_mut() {
            scene.update(delta_time);
        }
    }
}

impl Default for SceneManager {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::scene_graph::node::{Node2D, RenderableNode2D};

    #[test]
    fn test_scene_creation() {
        let scene = Scene::new("Test Scene".to_string());
        assert_eq!(scene.name(), "Test Scene");
        assert!(scene.root_id().is_none());
        assert_eq!(scene.node_count(), 0);
        assert!(scene.is_empty());
    }

    #[test]
    fn test_scene_add_node() {
        let mut scene = Scene::new("Test Scene".to_string());
        let node = Box::new(Node2D::new("Test Node".to_string()));
        let node_id = node.id();

        let added_id = scene.add_node(node);
        assert_eq!(added_id, node_id);
        assert_eq!(scene.node_count(), 1);
        assert!(!scene.is_empty());
        assert_eq!(scene.root_id(), Some(node_id)); // First node becomes root
    }

    #[test]
    fn test_scene_remove_node() {
        let mut scene = Scene::new("Test Scene".to_string());
        let node = Box::new(Node2D::new("Test Node".to_string()));
        let node_id = node.id();

        scene.add_node(node);
        assert_eq!(scene.node_count(), 1);

        let removed_node = scene.remove_node(node_id).unwrap();
        assert_eq!(removed_node.name(), "Test Node");
        assert_eq!(scene.node_count(), 0);
        assert!(scene.is_empty());
        assert!(scene.root_id().is_none());
    }

    #[test]
    fn test_scene_get_node() {
        let mut scene = Scene::new("Test Scene".to_string());
        let node = Box::new(Node2D::new("Test Node".to_string()));
        let node_id = node.id();

        scene.add_node(node);

        let retrieved_node = scene.get_node(node_id).unwrap();
        assert_eq!(retrieved_node.name(), "Test Node");
        assert_eq!(retrieved_node.id(), node_id);
    }

    #[test]
    fn test_scene_parent_child_relationships() {
        let mut scene = Scene::new("Test Scene".to_string());

        let parent_node = Box::new(Node2D::new("Parent".to_string()));
        let child_node = Box::new(Node2D::new("Child".to_string()));

        let parent_id = parent_node.id();
        let child_id = child_node.id();

        scene.add_node(parent_node);
        scene.add_node(child_node);

        // Add child to parent
        scene.add_child(parent_id, child_id).unwrap();

        let parent = scene.get_node(parent_id).unwrap();
        let child = scene.get_node(child_id).unwrap();

        assert!(parent.children().contains(&child_id));
        assert_eq!(child.parent(), Some(parent_id));
    }

    #[test]
    fn test_scene_remove_child() {
        let mut scene = Scene::new("Test Scene".to_string());

        let parent_node = Box::new(Node2D::new("Parent".to_string()));
        let child_node = Box::new(Node2D::new("Child".to_string()));

        let parent_id = parent_node.id();
        let child_id = child_node.id();

        scene.add_node(parent_node);
        scene.add_node(child_node);
        scene.add_child(parent_id, child_id).unwrap();

        // Remove child from parent
        scene.remove_child(parent_id, child_id).unwrap();

        let parent = scene.get_node(parent_id).unwrap();
        let child = scene.get_node(child_id).unwrap();

        assert!(!parent.children().contains(&child_id));
        assert_eq!(child.parent(), None);
    }

    #[test]
    fn test_scene_collect_renderable_nodes() {
        let mut scene = Scene::new("Test Scene".to_string());

        // Add a regular node (not renderable) as root
        let regular_node = Box::new(Node2D::new("Regular".to_string()));
        let regular_id = regular_node.id();
        scene.add_node(regular_node);

        // Add a renderable node as child of the root
        let renderable_node = Box::new(RenderableNode2D::triangle(
            "Triangle".to_string(),
            1.0,
            [1.0, 0.0, 0.0, 1.0]
        ));
        let renderable_id = renderable_node.id();
        scene.add_node(renderable_node);

        // Set up the hierarchy so the renderable node is a child of the root
        scene.add_child(regular_id, renderable_id).unwrap();

        let renderable_nodes = scene.collect_renderable_nodes();
        assert_eq!(renderable_nodes.len(), 1);
        assert_eq!(renderable_nodes[0].0, renderable_id);
    }

    #[test]
    fn test_scene_set_root() {
        let mut scene = Scene::new("Test Scene".to_string());

        let node1 = Box::new(Node2D::new("Node1".to_string()));
        let node2 = Box::new(Node2D::new("Node2".to_string()));

        let node1_id = node1.id();
        let node2_id = node2.id();

        scene.add_node(node1);
        scene.add_node(node2);

        // First node should be root by default
        assert_eq!(scene.root_id(), Some(node1_id));

        // Change root to second node
        scene.set_root(node2_id).unwrap();
        assert_eq!(scene.root_id(), Some(node2_id));
    }

    #[test]
    fn test_scene_clear() {
        let mut scene = Scene::new("Test Scene".to_string());

        let node = Box::new(Node2D::new("Test Node".to_string()));
        scene.add_node(node);

        assert!(!scene.is_empty());

        scene.clear();

        assert!(scene.is_empty());
        assert_eq!(scene.node_count(), 0);
        assert!(scene.root_id().is_none());
    }

    #[test]
    fn test_scene_manager_creation() {
        let manager = SceneManager::new();
        assert!(manager.get_active_scene().is_none());
    }

    #[test]
    fn test_scene_manager_add_scene() {
        let mut manager = SceneManager::new();
        let scene = Scene::new("Test Scene".to_string());

        manager.add_scene(scene);

        let active_scene = manager.get_active_scene().unwrap();
        assert_eq!(active_scene.name(), "Test Scene");
    }

    #[test]
    fn test_scene_manager_remove_scene() {
        let mut manager = SceneManager::new();
        let scene = Scene::new("Test Scene".to_string());

        manager.add_scene(scene);
        assert!(manager.get_active_scene().is_some());

        let removed_scene = manager.remove_scene("Test Scene").unwrap();
        assert_eq!(removed_scene.name(), "Test Scene");
        assert!(manager.get_active_scene().is_none());
    }

    #[test]
    fn test_scene_manager_set_active_scene() {
        let mut manager = SceneManager::new();

        let scene1 = Scene::new("Scene1".to_string());
        let scene2 = Scene::new("Scene2".to_string());

        manager.add_scene(scene1);
        manager.add_scene(scene2);

        // First scene should be active
        assert_eq!(manager.get_active_scene().unwrap().name(), "Scene1");

        // Switch to second scene
        manager.set_active_scene("Scene2").unwrap();
        assert_eq!(manager.get_active_scene().unwrap().name(), "Scene2");
    }

    #[test]
    fn test_scene_error_node_not_found() {
        let mut scene = Scene::new("Test Scene".to_string());
        let fake_id = NodeId::new();

        let result = scene.remove_node(fake_id);
        assert!(matches!(result, Err(SceneError::NodeNotFound(_))));
    }

    #[test]
    fn test_scene_error_invalid_state() {
        let mut manager = SceneManager::new();

        let result = manager.set_active_scene("NonExistent");
        assert!(matches!(result, Err(SceneError::InvalidState(_))));
    }
}
