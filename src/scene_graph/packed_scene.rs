use super::*;
use crate::resources::Resource;
use std::any::Any;
use std::collections::HashMap;

/// PackedScene resource - matches <PERSON><PERSON>'s PackedScene
#[derive(Debug, <PERSON>lone)]
pub struct PackedScene {
    /// Path to the scene file
    pub path: String,
    /// Serialized scene data
    pub scene_data: SerializedScene,
    /// Scene state for instancing
    pub state: SceneState,
}

impl PackedScene {
    /// Create a new packed scene
    pub fn new(path: String) -> Self {
        Self {
            path,
            scene_data: SerializedScene {
                name: "Scene".to_string(),
                version: "1.0".to_string(),
                nodes: Vec::new(),
                connections: Vec::new(),
                metadata: HashMap::new(),
            },
            state: SceneState::new(),
        }
    }
    
    /// Create a packed scene from a scene
    pub fn from_scene(scene: &Scene, path: String) -> Result<Self, SceneLoadError> {
        let loader = SceneLoader::new();
        let scene_data = loader.serialize_scene(scene)?;
        
        let state = SceneState::from_serialized(&scene_data);
        Ok(Self {
            path,
            scene_data,
            state,
        })
    }
    
    /// Load a packed scene from a file
    pub fn load(path: &str) -> Result<Self, SceneLoadError> {
        let mut loader = SceneLoader::new();
        let scene = loader.load_scene(path)?;
        Self::from_scene(&scene, path.to_string())
    }
    
    /// Save the packed scene to a file
    pub fn save(&self, path: &str) -> Result<(), SceneLoadError> {
        let loader = SceneLoader::new();
        // Create a temporary scene from the data
        let mut temp_loader = SceneLoader::new();
        let scene = temp_loader.deserialize_scene(self.scene_data.clone())?;
        loader.save_scene(&scene, path)
    }
    
    /// Instantiate the scene
    pub fn instantiate(&self) -> Result<Scene, SceneLoadError> {
        let mut loader = SceneLoader::new();
        loader.deserialize_scene(self.scene_data.clone())
    }
    
    /// Instantiate the scene with custom parameters
    pub fn instantiate_with_params(&self, params: InstanceParams) -> Result<Scene, SceneLoadError> {
        let mut scene = self.instantiate()?;
        
        // Apply custom parameters
        if let Some(name) = params.name {
            scene.set_name(name);
        }
        
        // Apply node modifications
        for (node_path, modifications) in params.node_modifications {
            if let Some(node_id) = scene.get_node_by_path(&node_path) {
                // Apply modifications to the node
                // This would require extending the Node trait with property setters
            }
        }
        
        Ok(scene)
    }
    
    /// Get the root node type
    pub fn get_root_node_type(&self) -> Option<&str> {
        self.scene_data.nodes.first().map(|n| n.node_type.as_str())
    }
    
    /// Get all node types in the scene
    pub fn get_node_types(&self) -> Vec<&str> {
        self.scene_data.nodes.iter().map(|n| n.node_type.as_str()).collect()
    }
    
    /// Check if the scene has a specific node type
    pub fn has_node_type(&self, node_type: &str) -> bool {
        self.scene_data.nodes.iter().any(|n| n.node_type == node_type)
    }
    
    /// Get scene metadata
    pub fn get_metadata(&self, key: &str) -> Option<&String> {
        self.scene_data.metadata.get(key)
    }
    
    /// Set scene metadata
    pub fn set_metadata(&mut self, key: String, value: String) {
        self.scene_data.metadata.insert(key, value);
    }
    
    /// Get the scene state
    pub fn get_state(&self) -> &SceneState {
        &self.state
    }
    
    /// Update the scene state
    pub fn update_state(&mut self, scene: &Scene) -> Result<(), SceneLoadError> {
        let loader = SceneLoader::new();
        self.scene_data = loader.serialize_scene(scene)?;
        self.state = SceneState::from_serialized(&self.scene_data);
        Ok(())
    }
}

impl Resource for PackedScene {
    fn as_any(&self) -> &dyn Any {
        self
    }
    
    fn get_type_name(&self) -> &'static str {
        "PackedScene"
    }
    
    fn get_path(&self) -> Option<&str> {
        Some(&self.path)
    }
    
    fn clone_resource(&self) -> Box<dyn Resource> {
        Box::new(self.clone())
    }
    
    fn get_memory_usage(&self) -> usize {
        std::mem::size_of::<Self>() + 
        self.path.len() +
        self.scene_data.nodes.len() * std::mem::size_of::<SerializedNode>() +
        self.scene_data.connections.len() * std::mem::size_of::<SerializedConnection>()
    }
}

/// Scene state for tracking scene structure
#[derive(Debug, Clone)]
pub struct SceneState {
    /// Node count
    pub node_count: usize,
    /// Connection count
    pub connection_count: usize,
    /// Node names
    pub node_names: Vec<String>,
    /// Node types
    pub node_types: Vec<String>,
    /// Scene version
    pub version: String,
}

impl SceneState {
    /// Create a new scene state
    pub fn new() -> Self {
        Self {
            node_count: 0,
            connection_count: 0,
            node_names: Vec::new(),
            node_types: Vec::new(),
            version: "1.0".to_string(),
        }
    }
    
    /// Create scene state from serialized data
    pub fn from_serialized(data: &SerializedScene) -> Self {
        Self {
            node_count: data.nodes.len(),
            connection_count: data.connections.len(),
            node_names: data.nodes.iter().map(|n| n.name.clone()).collect(),
            node_types: data.nodes.iter().map(|n| n.node_type.clone()).collect(),
            version: data.version.clone(),
        }
    }
}

impl Default for SceneState {
    fn default() -> Self {
        Self::new()
    }
}

/// Parameters for scene instantiation
#[derive(Debug, Default)]
pub struct InstanceParams {
    /// Custom name for the instantiated scene
    pub name: Option<String>,
    /// Node modifications to apply
    pub node_modifications: HashMap<String, NodeModifications>,
    /// Whether to make the instance editable
    pub editable: bool,
}

/// Modifications to apply to a node during instantiation
#[derive(Debug, Default)]
pub struct NodeModifications {
    /// Property overrides
    pub properties: HashMap<String, SerializedProperty>,
    /// Whether to replace the node entirely
    pub replace: bool,
    /// Custom script to attach
    pub script: Option<String>,
}

/// Scene manager for handling multiple scenes and scene switching
pub struct PackedSceneManager {
    /// Currently active scene
    current_scene: Option<Scene>,
    /// Scene stack for scene transitions
    scene_stack: Vec<Scene>,
    /// Loaded packed scenes cache
    packed_scenes: HashMap<String, PackedScene>,
    /// Scene loader
    loader: SceneLoader,
}

impl PackedSceneManager {
    /// Create a new scene manager
    pub fn new() -> Self {
        Self {
            current_scene: None,
            scene_stack: Vec::new(),
            packed_scenes: HashMap::new(),
            loader: SceneLoader::new(),
        }
    }
    
    /// Load and set the current scene
    pub fn change_scene(&mut self, path: &str) -> Result<(), SceneLoadError> {
        let scene = self.loader.load_scene(path)?;
        self.current_scene = Some(scene);
        Ok(())
    }
    
    /// Load and set the current scene from a packed scene
    pub fn change_scene_to_packed(&mut self, packed_scene: &PackedScene) -> Result<(), SceneLoadError> {
        let scene = packed_scene.instantiate()?;
        self.current_scene = Some(scene);
        Ok(())
    }
    
    /// Push the current scene to the stack and load a new one
    pub fn push_scene(&mut self, path: &str) -> Result<(), SceneLoadError> {
        if let Some(current) = self.current_scene.take() {
            self.scene_stack.push(current);
        }
        self.change_scene(path)
    }
    
    /// Pop the previous scene from the stack
    pub fn pop_scene(&mut self) -> bool {
        if let Some(scene) = self.scene_stack.pop() {
            self.current_scene = Some(scene);
            true
        } else {
            false
        }
    }
    
    /// Get the current scene
    pub fn get_current_scene(&self) -> Option<&Scene> {
        self.current_scene.as_ref()
    }
    
    /// Get the current scene mutably
    pub fn get_current_scene_mut(&mut self) -> Option<&mut Scene> {
        self.current_scene.as_mut()
    }
    
    /// Load a packed scene and cache it
    pub fn load_packed_scene(&mut self, path: &str) -> Result<&PackedScene, SceneLoadError> {
        if !self.packed_scenes.contains_key(path) {
            let packed_scene = PackedScene::load(path)?;
            self.packed_scenes.insert(path.to_string(), packed_scene);
        }
        Ok(self.packed_scenes.get(path).unwrap())
    }
    
    /// Get a cached packed scene
    pub fn get_packed_scene(&self, path: &str) -> Option<&PackedScene> {
        self.packed_scenes.get(path)
    }
    
    /// Instantiate a scene from a packed scene
    pub fn instantiate_scene(&self, path: &str) -> Result<Scene, SceneLoadError> {
        if let Some(packed_scene) = self.packed_scenes.get(path) {
            packed_scene.instantiate()
        } else {
            PackedScene::load(path)?.instantiate()
        }
    }
    
    /// Save the current scene
    pub fn save_current_scene(&self, path: &str) -> Result<(), SceneLoadError> {
        if let Some(scene) = &self.current_scene {
            self.loader.save_scene(scene, path)
        } else {
            Err(SceneLoadError::InvalidScene("No current scene to save".to_string()))
        }
    }
    
    /// Update the current scene
    pub fn update(&mut self, delta_time: f32) {
        if let Some(scene) = &mut self.current_scene {
            scene.update(delta_time);
        }
    }
    
    /// Clear all cached packed scenes
    pub fn clear_cache(&mut self) {
        self.packed_scenes.clear();
    }
    
    /// Get scene stack depth
    pub fn get_scene_stack_depth(&self) -> usize {
        self.scene_stack.len()
    }
    
    /// Check if there's a current scene
    pub fn has_current_scene(&self) -> bool {
        self.current_scene.is_some()
    }
}

impl Default for PackedSceneManager {
    fn default() -> Self {
        Self::new()
    }
}

impl std::fmt::Debug for PackedSceneManager {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("PackedSceneManager")
            .field("has_current_scene", &self.current_scene.is_some())
            .field("scene_stack_depth", &self.scene_stack.len())
            .field("packed_scenes_count", &self.packed_scenes.len())
            .finish()
    }
}
