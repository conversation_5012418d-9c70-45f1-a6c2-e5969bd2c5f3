use super::*;
use nalgebra::Vector2;

/// Body mode for RigidBody2D
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Eq)]
pub enum RigidBodyMode {
    /// Static body (doesn't move)
    Static,
    /// Kinematic body (moves via code)
    Kinematic,
    /// Dynamic body (moves via physics)
    Dynamic,
    /// Character body (for character controllers)
    Character,
}

impl Default for RigidBodyMode {
    fn default() -> Self {
        RigidBodyMode::Dynamic
    }
}

/// CollisionShape2D node - matches <PERSON><PERSON>'s CollisionShape2D
#[derive(Debug)]
pub struct CollisionShape2D {
    node2d: BaseNode2D,
    shape: CollisionShape,
    disabled: bool,
    one_way_collision: bool,
    one_way_collision_margin: f32,
}

impl CollisionShape2D {
    /// Create a new CollisionShape2D
    pub fn new(name: String) -> Self {
        Self {
            node2d: BaseNode2D::new(name),
            shape: CollisionShape::Rectangle { width: 1.0, height: 1.0 },
            disabled: false,
            one_way_collision: false,
            one_way_collision_margin: 1.0,
        }
    }
    
    /// Create a rectangle collision shape
    pub fn rectangle(name: String, width: f32, height: f32) -> Self {
        let mut shape = Self::new(name);
        shape.shape = CollisionShape::Rectangle { width, height };
        shape
    }
    
    /// Create a circle collision shape
    pub fn circle(name: String, radius: f32) -> Self {
        let mut shape = Self::new(name);
        shape.shape = CollisionShape::Circle { radius };
        shape
    }
    
    /// Set the collision shape
    pub fn set_shape(&mut self, shape: CollisionShape) {
        self.shape = shape;
    }
    
    /// Get the collision shape
    pub fn shape(&self) -> &CollisionShape {
        &self.shape
    }
    
    /// Set disabled state
    pub fn set_disabled(&mut self, disabled: bool) {
        self.disabled = disabled;
    }
    
    /// Check if disabled
    pub fn is_disabled(&self) -> bool {
        self.disabled
    }
}

/// Collision shape types
#[derive(Debug, Clone)]
pub enum CollisionShape {
    Rectangle { width: f32, height: f32 },
    Circle { radius: f32 },
    Capsule { height: f32, radius: f32 },
    Polygon { points: Vec<Vector2<f32>> },
}

/// RigidBody2D node - matches Godot's RigidBody2D
#[derive(Debug)]
pub struct RigidBody2D {
    node2d: BaseNode2D,
    mode: RigidBodyMode,
    mass: f32,
    gravity_scale: f32,
    linear_velocity: Vector2<f32>,
    angular_velocity: f32,
    linear_damp: f32,
    angular_damp: f32,
    can_sleep: bool,
    lock_rotation: bool,
    freeze_x: bool,
    freeze_y: bool,
}

impl RigidBody2D {
    /// Create a new RigidBody2D
    pub fn new(name: String) -> Self {
        Self {
            node2d: BaseNode2D::new(name),
            mode: RigidBodyMode::default(),
            mass: 1.0,
            gravity_scale: 1.0,
            linear_velocity: Vector2::new(0.0, 0.0),
            angular_velocity: 0.0,
            linear_damp: 0.0,
            angular_damp: 0.0,
            can_sleep: true,
            lock_rotation: false,
            freeze_x: false,
            freeze_y: false,
        }
    }
    
    /// Set the body mode
    pub fn set_mode(&mut self, mode: RigidBodyMode) {
        self.mode = mode;
    }
    
    /// Get the body mode
    pub fn mode(&self) -> RigidBodyMode {
        self.mode
    }
    
    /// Set mass
    pub fn set_mass(&mut self, mass: f32) {
        self.mass = mass;
    }
    
    /// Get mass
    pub fn mass(&self) -> f32 {
        self.mass
    }
    
    /// Set linear velocity
    pub fn set_linear_velocity(&mut self, velocity: Vector2<f32>) {
        self.linear_velocity = velocity;
    }
    
    /// Get linear velocity
    pub fn linear_velocity(&self) -> Vector2<f32> {
        self.linear_velocity
    }
    
    /// Apply impulse
    pub fn apply_impulse(&mut self, impulse: Vector2<f32>, position: Option<Vector2<f32>>) {
        // In a real implementation, this would interact with the physics engine
        self.linear_velocity += impulse / self.mass;
        
        if let Some(_pos) = position {
            // Apply torque based on position offset
            // This is a simplified implementation
        }
    }
    
    /// Apply force
    pub fn apply_force(&mut self, force: Vector2<f32>, position: Option<Vector2<f32>>) {
        // In a real implementation, this would accumulate forces for the physics step
        let acceleration = force / self.mass;
        // This would be applied during physics update
        
        if let Some(_pos) = position {
            // Apply torque based on position offset
        }
    }
}

/// Area2D node - matches Godot's Area2D
#[derive(Debug)]
pub struct Area2D {
    node2d: BaseNode2D,
    monitoring: bool,
    monitorable: bool,
    priority: i32,
    gravity_space_override: SpaceOverride,
    gravity: f32,
    gravity_vector: Vector2<f32>,
    linear_damp_space_override: SpaceOverride,
    linear_damp: f32,
    angular_damp_space_override: SpaceOverride,
    angular_damp: f32,
}

impl Area2D {
    /// Create a new Area2D
    pub fn new(name: String) -> Self {
        Self {
            node2d: BaseNode2D::new(name),
            monitoring: true,
            monitorable: true,
            priority: 0,
            gravity_space_override: SpaceOverride::Disabled,
            gravity: 980.0,
            gravity_vector: Vector2::new(0.0, 1.0),
            linear_damp_space_override: SpaceOverride::Disabled,
            linear_damp: 0.1,
            angular_damp_space_override: SpaceOverride::Disabled,
            angular_damp: 1.0,
        }
    }
    
    /// Set monitoring
    pub fn set_monitoring(&mut self, monitoring: bool) {
        self.monitoring = monitoring;
    }
    
    /// Check if monitoring
    pub fn is_monitoring(&self) -> bool {
        self.monitoring
    }
    
    /// Set monitorable
    pub fn set_monitorable(&mut self, monitorable: bool) {
        self.monitorable = monitorable;
    }
    
    /// Check if monitorable
    pub fn is_monitorable(&self) -> bool {
        self.monitorable
    }
    
    /// Get overlapping bodies (placeholder)
    pub fn get_overlapping_bodies(&self) -> Vec<NodeId> {
        // In a real implementation, this would query the physics engine
        Vec::new()
    }
    
    /// Get overlapping areas (placeholder)
    pub fn get_overlapping_areas(&self) -> Vec<NodeId> {
        // In a real implementation, this would query the physics engine
        Vec::new()
    }
}

/// Space override modes for Area2D
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum SpaceOverride {
    Disabled,
    Combine,
    CombineReplace,
    Replace,
    ReplaceCombin,
}

impl Default for SpaceOverride {
    fn default() -> Self {
        SpaceOverride::Disabled
    }
}

/// CharacterBody2D node - matches Godot's CharacterBody2D
#[derive(Debug)]
pub struct CharacterBody2D {
    node2d: BaseNode2D,
    velocity: Vector2<f32>,
    up_direction: Vector2<f32>,
    motion_mode: MotionMode,
    platform_floor_layers: u32,
    platform_wall_layers: u32,
    floor_stop_on_slope: bool,
    floor_constant_speed: bool,
    floor_block_on_wall: bool,
    floor_max_angle: f32,
    floor_snap_length: f32,
    wall_min_slide_angle: f32,
    safe_margin: f32,
}

impl CharacterBody2D {
    /// Create a new CharacterBody2D
    pub fn new(name: String) -> Self {
        Self {
            node2d: BaseNode2D::new(name),
            velocity: Vector2::new(0.0, 0.0),
            up_direction: Vector2::new(0.0, -1.0),
            motion_mode: MotionMode::Grounded,
            platform_floor_layers: 0xFFFFFFFF,
            platform_wall_layers: 0,
            floor_stop_on_slope: true,
            floor_constant_speed: false,
            floor_block_on_wall: true,
            floor_max_angle: std::f32::consts::PI / 4.0, // 45 degrees
            floor_snap_length: 1.0,
            wall_min_slide_angle: std::f32::consts::PI / 6.0, // 15 degrees
            safe_margin: 0.08,
        }
    }
    
    /// Set velocity
    pub fn set_velocity(&mut self, velocity: Vector2<f32>) {
        self.velocity = velocity;
    }
    
    /// Get velocity
    pub fn velocity(&self) -> Vector2<f32> {
        self.velocity
    }
    
    /// Move and slide (placeholder)
    pub fn move_and_slide(&mut self) -> bool {
        // In a real implementation, this would perform collision detection
        // and slide along surfaces
        false
    }
    
    /// Check if on floor
    pub fn is_on_floor(&self) -> bool {
        // In a real implementation, this would check collision state
        false
    }
    
    /// Check if on wall
    pub fn is_on_wall(&self) -> bool {
        // In a real implementation, this would check collision state
        false
    }
    
    /// Check if on ceiling
    pub fn is_on_ceiling(&self) -> bool {
        // In a real implementation, this would check collision state
        false
    }
}

/// Motion mode for CharacterBody2D
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum MotionMode {
    Grounded,
    Floating,
}

impl Default for MotionMode {
    fn default() -> Self {
        MotionMode::Grounded
    }
}

// Implement Node trait for all physics nodes
macro_rules! impl_node_for_physics {
    ($type:ty, $field:ident) => {
        impl Node for $type {
            fn id(&self) -> NodeId {
                self.$field.id()
            }

            fn name(&self) -> &str {
                self.$field.name()
            }

            fn set_name(&mut self, name: String) {
                self.$field.set_name(name);
            }

            fn parent(&self) -> Option<NodeId> {
                self.$field.parent()
            }

            fn children(&self) -> &[NodeId] {
                self.$field.children()
            }

            fn process_mode(&self) -> ProcessMode {
                self.$field.process_mode()
            }

            fn set_process_mode(&mut self, mode: ProcessMode) {
                self.$field.set_process_mode(mode);
            }

            fn is_inside_tree(&self) -> bool {
                self.$field.is_inside_tree()
            }

            fn is_node_ready(&self) -> bool {
                self.$field.is_node_ready()
            }

            fn get_groups(&self) -> &[String] {
                self.$field.get_groups()
            }

            fn add_to_group(&mut self, group: String) {
                self.$field.add_to_group(group);
            }

            fn remove_from_group(&mut self, group: &str) {
                self.$field.remove_from_group(group);
            }

            fn is_in_group(&self, group: &str) -> bool {
                self.$field.is_in_group(group)
            }

            fn add_child(&mut self, child_id: NodeId) {
                self.$field.add_child(child_id);
            }

            fn remove_child(&mut self, child_id: NodeId) {
                self.$field.remove_child(child_id);
            }

            fn set_parent(&mut self, parent_id: Option<NodeId>) {
                self.$field.set_parent(parent_id);
            }

            fn get_path(&self, scene: &Scene) -> String {
                self.$field.get_path(scene)
            }

            fn _enter_tree(&mut self, scene: &mut Scene) {
                self.$field._enter_tree(scene);
            }

            fn _ready(&mut self, scene: &mut Scene) {
                self.$field._ready(scene);
            }

            fn _exit_tree(&mut self, scene: &mut Scene) {
                self.$field._exit_tree(scene);
            }

            fn as_any(&self) -> &dyn std::any::Any {
                self
            }

            fn as_any_mut(&mut self) -> &mut dyn std::any::Any {
                self
            }
        }

        impl CanvasItem for $type {
            fn z_index(&self) -> i32 {
                self.$field.z_index()
            }

            fn set_z_index(&mut self, z_index: i32) {
                self.$field.set_z_index(z_index);
            }

            fn is_z_relative(&self) -> bool {
                self.$field.is_z_relative()
            }

            fn set_z_as_relative(&mut self, relative: bool) {
                self.$field.set_z_as_relative(relative);
            }

            fn is_visible(&self) -> bool {
                self.$field.is_visible()
            }

            fn set_visible(&mut self, visible: bool) {
                self.$field.set_visible(visible);
            }

            fn modulate(&self) -> [f32; 4] {
                self.$field.modulate()
            }

            fn set_modulate(&mut self, color: [f32; 4]) {
                self.$field.set_modulate(color);
            }

            fn self_modulate(&self) -> [f32; 4] {
                self.$field.self_modulate()
            }

            fn set_self_modulate(&mut self, color: [f32; 4]) {
                self.$field.set_self_modulate(color);
            }

            fn blend_mode(&self) -> BlendMode {
                self.$field.blend_mode()
            }

            fn set_blend_mode(&mut self, mode: BlendMode) {
                self.$field.set_blend_mode(mode);
            }
        }

        impl Node2D for $type {
            fn transform(&self) -> &Transform2D {
                self.$field.transform()
            }

            fn transform_mut(&mut self) -> &mut Transform2D {
                self.$field.transform_mut()
            }

            fn global_transform(&self, scene: &Scene) -> Transform2D {
                self.$field.global_transform(scene)
            }
        }
    };
}

impl_node_for_physics!(CollisionShape2D, node2d);
impl_node_for_physics!(RigidBody2D, node2d);
impl_node_for_physics!(Area2D, node2d);
impl_node_for_physics!(CharacterBody2D, node2d);
