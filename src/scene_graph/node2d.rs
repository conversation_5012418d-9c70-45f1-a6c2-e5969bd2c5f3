use super::*;
use nalgebra::Vector2;

/// Node2D trait - matches <PERSON><PERSON>'s Node2D class
/// Base class for all 2D nodes with transform
pub trait Node2D: CanvasItem {
    /// Get the local transform
    fn transform(&self) -> &Transform2D;
    
    /// Get the local transform mutably
    fn transform_mut(&mut self) -> &mut Transform2D;
    
    /// Get the global transform
    fn global_transform(&self, scene: &Scene) -> Transform2D;
    
    /// Get position
    fn position(&self) -> Vector2<f32> {
        self.transform().position
    }
    
    /// Set position
    fn set_position(&mut self, position: Vector2<f32>) {
        self.transform_mut().set_position(position);
    }
    
    /// Get rotation in radians
    fn rotation(&self) -> f32 {
        self.transform().rotation
    }
    
    /// Set rotation in radians
    fn set_rotation(&mut self, rotation: f32) {
        self.transform_mut().set_rotation(rotation);
    }
    
    /// Get scale
    fn scale(&self) -> Vector2<f32> {
        self.transform().scale
    }
    
    /// Set scale
    fn set_scale(&mut self, scale: Vector2<f32>) {
        self.transform_mut().set_scale(scale);
    }
    
    /// Translate by offset
    fn translate(&mut self, offset: Vector2<f32>) {
        self.transform_mut().translate(offset);
    }
    
    /// Rotate by angle
    fn rotate(&mut self, angle: f32) {
        self.transform_mut().rotate(angle);
    }
    
    /// Look at a point
    fn look_at(&mut self, point: Vector2<f32>) {
        let direction = point - self.position();
        let angle = direction.y.atan2(direction.x);
        self.set_rotation(angle);
    }
    
    /// Get the angle to a point
    fn angle_to_point(&self, point: Vector2<f32>) -> f32 {
        let direction = point - self.position();
        direction.y.atan2(direction.x)
    }
    
    /// Get distance to a point
    fn distance_to(&self, point: Vector2<f32>) -> f32 {
        (point - self.position()).magnitude()
    }
}

/// Base implementation of Node2D
#[derive(Debug)]
pub struct BaseNode2D {
    canvas_item: BaseCanvasItem,
    transform: Transform2D,
}

impl BaseNode2D {
    /// Create a new Node2D
    pub fn new(name: String) -> Self {
        Self {
            canvas_item: BaseCanvasItem::new(name),
            transform: Transform2D::default(),
        }
    }
    
    /// Create a new Node2D with position
    pub fn with_position(name: String, position: Vector2<f32>) -> Self {
        let mut node = Self::new(name);
        node.transform.set_position(position);
        node
    }
    
    /// Get the canvas item
    pub fn canvas_item(&self) -> &BaseCanvasItem {
        &self.canvas_item
    }
    
    /// Get the canvas item mutably
    pub fn canvas_item_mut(&mut self) -> &mut BaseCanvasItem {
        &mut self.canvas_item
    }
}

impl Node for BaseNode2D {
    fn id(&self) -> NodeId {
        self.canvas_item.id()
    }
    
    fn name(&self) -> &str {
        self.canvas_item.name()
    }
    
    fn set_name(&mut self, name: String) {
        self.canvas_item.set_name(name);
    }
    
    fn parent(&self) -> Option<NodeId> {
        self.canvas_item.parent()
    }
    
    fn children(&self) -> &[NodeId] {
        self.canvas_item.children()
    }
    
    fn process_mode(&self) -> ProcessMode {
        self.canvas_item.process_mode()
    }
    
    fn set_process_mode(&mut self, mode: ProcessMode) {
        self.canvas_item.set_process_mode(mode);
    }
    
    fn is_inside_tree(&self) -> bool {
        self.canvas_item.is_inside_tree()
    }
    
    fn is_node_ready(&self) -> bool {
        self.canvas_item.is_node_ready()
    }
    
    fn get_groups(&self) -> &[String] {
        self.canvas_item.get_groups()
    }
    
    fn add_to_group(&mut self, group: String) {
        self.canvas_item.add_to_group(group);
    }
    
    fn remove_from_group(&mut self, group: &str) {
        self.canvas_item.remove_from_group(group);
    }
    
    fn is_in_group(&self, group: &str) -> bool {
        self.canvas_item.is_in_group(group)
    }
    
    fn add_child(&mut self, child_id: NodeId) {
        self.canvas_item.add_child(child_id);
    }
    
    fn remove_child(&mut self, child_id: NodeId) {
        self.canvas_item.remove_child(child_id);
    }
    
    fn set_parent(&mut self, parent_id: Option<NodeId>) {
        self.canvas_item.set_parent(parent_id);
    }
    
    fn get_path(&self, scene: &Scene) -> String {
        self.canvas_item.get_path(scene)
    }
    
    fn _enter_tree(&mut self, scene: &mut Scene) {
        self.canvas_item._enter_tree(scene);
    }
    
    fn _ready(&mut self, scene: &mut Scene) {
        self.canvas_item._ready(scene);
    }
    
    fn _exit_tree(&mut self, scene: &mut Scene) {
        self.canvas_item._exit_tree(scene);
    }
    
    fn _notification(&mut self, what: Notification, scene: &mut Scene) {
        self.canvas_item._notification(what, scene);

        match what {
            Notification::TransformChanged => {
                // Handle transform change
            }
            Notification::LocalTransformChanged => {
                // Handle local transform change
            }
            _ => {}
        }
    }

    fn as_any(&self) -> &dyn std::any::Any {
        self
    }

    fn as_any_mut(&mut self) -> &mut dyn std::any::Any {
        self
    }
}

impl CanvasItem for BaseNode2D {
    fn z_index(&self) -> i32 {
        self.canvas_item.z_index()
    }
    
    fn set_z_index(&mut self, z_index: i32) {
        self.canvas_item.set_z_index(z_index);
    }
    
    fn is_z_relative(&self) -> bool {
        self.canvas_item.is_z_relative()
    }
    
    fn set_z_as_relative(&mut self, relative: bool) {
        self.canvas_item.set_z_as_relative(relative);
    }
    
    fn is_visible(&self) -> bool {
        self.canvas_item.is_visible()
    }
    
    fn set_visible(&mut self, visible: bool) {
        self.canvas_item.set_visible(visible);
    }
    
    fn modulate(&self) -> [f32; 4] {
        self.canvas_item.modulate()
    }
    
    fn set_modulate(&mut self, color: [f32; 4]) {
        self.canvas_item.set_modulate(color);
    }
    
    fn self_modulate(&self) -> [f32; 4] {
        self.canvas_item.self_modulate()
    }
    
    fn set_self_modulate(&mut self, color: [f32; 4]) {
        self.canvas_item.set_self_modulate(color);
    }
    
    fn blend_mode(&self) -> BlendMode {
        self.canvas_item.blend_mode()
    }
    
    fn set_blend_mode(&mut self, mode: BlendMode) {
        self.canvas_item.set_blend_mode(mode);
    }
    
    fn _draw(&self, render_context: &RenderContext) {
        self.canvas_item._draw(render_context);
    }
}

impl Node2D for BaseNode2D {
    fn transform(&self) -> &Transform2D {
        &self.transform
    }
    
    fn transform_mut(&mut self) -> &mut Transform2D {
        &mut self.transform
    }
    
    fn global_transform(&self, scene: &Scene) -> Transform2D {
        if let Some(parent_id) = self.parent() {
            if let Some(parent) = scene.get_node(parent_id) {
                // Try to get parent's global transform
                // In a real implementation, we'd use proper downcasting
                // For now, we'll just return the local transform
                self.transform.clone()
            } else {
                self.transform.clone()
            }
        } else {
            self.transform.clone()
        }
    }
}
