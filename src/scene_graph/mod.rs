use std::sync::atomic::{AtomicU64, Ordering};

pub mod node;
pub mod canvas_item;
pub mod node2d;
pub mod scene;
pub mod transform;
pub mod notifications;
pub mod physics_nodes;
pub mod ui_nodes;
pub mod signals;

pub use node::*;
pub use canvas_item::*;
pub use node2d::*;
pub use scene::*;
pub use transform::*;
pub use notifications::*;
pub use physics_nodes::*;
pub use ui_nodes::*;
pub use signals::*;

/// Unique identifier for nodes in the scene graph
#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq, Hash)]
pub struct NodeId(pub u64);

impl NodeId {
    /// Generate a new unique NodeId
    pub fn new() -> Self {
        static COUNTER: AtomicU64 = AtomicU64::new(1);
        Self(COUNTER.fetch_add(1, Ordering::Relaxed))
    }
}

impl Default for NodeId {
    fn default() -> Self {
        Self::new()
    }
}

/// Process mode for nodes
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>q)]
pub enum ProcessMode {
    /// Inherit the process mode from the parent node
    Inherit,
    /// Always process, even when the scene tree is paused
    Always,
    /// Only process when the scene tree is not paused
    WhenNotPaused,
    /// Never process
    Disabled,
}

impl Default for ProcessMode {
    fn default() -> Self {
        ProcessMode::Inherit
    }
}

/// Core trait for all nodes in the scene graph - matches Godot's Node class
pub trait Node {
    /// Get the unique identifier for this node
    fn id(&self) -> NodeId;

    /// Get the name of this node
    fn name(&self) -> &str;

    /// Set the name of this node
    fn set_name(&mut self, name: String);

    /// Get the parent node ID, if any
    fn parent(&self) -> Option<NodeId>;

    /// Get the children node IDs
    fn children(&self) -> &[NodeId];

    /// Get the process mode
    fn process_mode(&self) -> ProcessMode;

    /// Set the process mode
    fn set_process_mode(&mut self, mode: ProcessMode);

    /// Check if the node is inside the scene tree
    fn is_inside_tree(&self) -> bool;

    /// Check if the node is ready (has been through _ready)
    fn is_node_ready(&self) -> bool;

    /// Get groups this node belongs to
    fn get_groups(&self) -> &[String];

    /// Add node to a group
    fn add_to_group(&mut self, group: String);

    /// Remove node from a group
    fn remove_from_group(&mut self, group: &str);

    /// Check if node is in a group
    fn is_in_group(&self, group: &str) -> bool;

    // Lifecycle methods (Godot-style)

    /// Called when the node enters the scene tree
    fn _enter_tree(&mut self, _scene: &mut Scene) {}

    /// Called when the node is ready (after _enter_tree and all children are ready)
    fn _ready(&mut self, _scene: &mut Scene) {}

    /// Called every frame
    fn _process(&mut self, _delta: f32, _scene: &mut Scene) {}

    /// Called every physics frame
    fn _physics_process(&mut self, _delta: f32, _scene: &mut Scene) {}

    /// Called when the node exits the scene tree
    fn _exit_tree(&mut self, _scene: &mut Scene) {}

    /// Handle notifications
    fn _notification(&mut self, what: Notification, _scene: &mut Scene) {
        match what {
            Notification::EnterTree => self._enter_tree(_scene),
            Notification::Ready => self._ready(_scene),
            Notification::ExitTree => self._exit_tree(_scene),
            _ => {}
        }
    }

    /// Add a child node ID to this node
    fn add_child(&mut self, child_id: NodeId);

    /// Remove a child node ID from this node
    fn remove_child(&mut self, child_id: NodeId);

    /// Set the parent of this node
    fn set_parent(&mut self, parent_id: Option<NodeId>);

    /// Get the node path from the scene root
    fn get_path(&self, scene: &Scene) -> String;
}

/// Trait for nodes that can provide render data
pub trait Renderable {
    /// Get the render data for this node
    fn get_render_data(&self) -> RenderData;

    /// Check if this node should be rendered
    fn should_render(&self) -> bool {
        true
    }
}

/// Data needed to render a node
#[derive(Debug, Clone)]
pub struct RenderData {
    pub vertices: Vec<Vertex>,
    pub indices: Vec<u16>,
    pub color: [f32; 4],
}

/// Vertex data for rendering
#[repr(C)]
#[derive(Debug, Clone, Copy, PartialEq, bytemuck::Pod, bytemuck::Zeroable)]
pub struct Vertex {
    pub position: [f32; 2],
}

impl Vertex {
    pub fn desc<'a>() -> wgpu::VertexBufferLayout<'a> {
        wgpu::VertexBufferLayout {
            array_stride: std::mem::size_of::<Vertex>() as wgpu::BufferAddress,
            step_mode: wgpu::VertexStepMode::Vertex,
            attributes: &[
                wgpu::VertexAttribute {
                    offset: 0,
                    shader_location: 0,
                    format: wgpu::VertexFormat::Float32x2,
                },
            ],
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_node_id_generation() {
        let id1 = NodeId::new();
        let id2 = NodeId::new();
        assert_ne!(id1, id2);
        assert!(id1.0 > 0);
        assert!(id2.0 > 0);
    }

    #[test]
    fn test_base_node_creation() {
        let node = BaseNode::new("Test Node".to_string());
        assert_eq!(node.name(), "Test Node");
        assert!(node.parent().is_none());
        assert!(node.children().is_empty());
    }

    #[test]
    fn test_base_node_parent_child_relationships() {
        let mut parent = BaseNode::new("Parent".to_string());
        let mut child = BaseNode::new("Child".to_string());

        let parent_id = parent.id();
        let child_id = child.id();

        // Add child to parent
        parent.add_child(child_id);
        child.set_parent(Some(parent_id));

        assert_eq!(parent.children(), &[child_id]);
        assert_eq!(child.parent(), Some(parent_id));

        // Remove child from parent
        parent.remove_child(child_id);
        child.set_parent(None);

        assert!(parent.children().is_empty());
        assert!(child.parent().is_none());
    }

    #[test]
    fn test_vertex_equality() {
        let v1 = Vertex { position: [1.0, 2.0] };
        let v2 = Vertex { position: [1.0, 2.0] };
        let v3 = Vertex { position: [2.0, 1.0] };

        assert_eq!(v1, v2);
        assert_ne!(v1, v3);
    }

    #[test]
    fn test_render_data_creation() {
        let vertices = vec![
            Vertex { position: [0.0, 1.0] },
            Vertex { position: [-1.0, -1.0] },
            Vertex { position: [1.0, -1.0] },
        ];
        let indices = vec![0, 1, 2];
        let color = [1.0, 0.0, 0.0, 1.0];

        let render_data = RenderData {
            vertices: vertices.clone(),
            indices: indices.clone(),
            color,
        };

        assert_eq!(render_data.vertices, vertices);
        assert_eq!(render_data.indices, indices);
        assert_eq!(render_data.color, color);
    }
}
