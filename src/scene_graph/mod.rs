use std::sync::atomic::{AtomicU64, Ordering};

pub mod node;
pub mod scene;
pub mod transform;

pub use node::*;
pub use scene::*;
pub use transform::*;

/// Unique identifier for nodes in the scene graph
#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>q, <PERSON>q, <PERSON>h)]
pub struct NodeId(pub u64);

impl NodeId {
    /// Generate a new unique NodeId
    pub fn new() -> Self {
        static COUNTER: AtomicU64 = AtomicU64::new(1);
        Self(COUNTER.fetch_add(1, Ordering::Relaxed))
    }
}

impl Default for NodeId {
    fn default() -> Self {
        Self::new()
    }
}

/// Core trait for all nodes in the scene graph
pub trait Node {
    /// Get the unique identifier for this node
    fn id(&self) -> NodeId;
    
    /// Get the name of this node
    fn name(&self) -> &str;
    
    /// Set the name of this node
    fn set_name(&mut self, name: String);
    
    /// Get the parent node ID, if any
    fn parent(&self) -> Option<NodeId>;
    
    /// Get the children node IDs
    fn children(&self) -> &[NodeId];
    
    /// Get the local transform of this node
    fn local_transform(&self) -> &Transform2D;
    
    /// Get a mutable reference to the local transform
    fn local_transform_mut(&mut self) -> &mut Transform2D;
    
    /// Calculate the global transform by combining with parent transforms
    fn global_transform(&self, scene: &Scene) -> Transform2D;
    
    /// Called when the node is added to the scene
    fn on_enter_scene(&mut self, _scene: &mut Scene) {}
    
    /// Called when the node is removed from the scene
    fn on_exit_scene(&mut self, _scene: &mut Scene) {}
    
    /// Called every frame to update the node
    fn update(&mut self, _delta_time: f32, _scene: &mut Scene) {}
    
    /// Called to render the node (if it's renderable)
    fn render(&self, _render_context: &RenderContext) {}
    
    /// Check if this node should be rendered
    fn is_renderable(&self) -> bool {
        false
    }
    
    /// Add a child node ID to this node
    fn add_child(&mut self, child_id: NodeId);
    
    /// Remove a child node ID from this node
    fn remove_child(&mut self, child_id: NodeId);
    
    /// Set the parent of this node
    fn set_parent(&mut self, parent_id: Option<NodeId>);
}

/// Context passed to nodes during rendering
pub struct RenderContext<'a> {
    pub device: &'a wgpu::Device,
    pub queue: &'a wgpu::Queue,
    pub render_pass: &'a mut wgpu::RenderPass<'a>,
    pub surface_config: &'a wgpu::SurfaceConfiguration,
}

/// Base implementation of a scene graph node
#[derive(Debug)]
pub struct BaseNode {
    id: NodeId,
    name: String,
    parent: Option<NodeId>,
    children: Vec<NodeId>,
    local_transform: Transform2D,
}

impl BaseNode {
    /// Create a new base node with the given name
    pub fn new(name: String) -> Self {
        Self {
            id: NodeId::new(),
            name,
            parent: None,
            children: Vec::new(),
            local_transform: Transform2D::default(),
        }
    }
}

impl Node for BaseNode {
    fn id(&self) -> NodeId {
        self.id
    }
    
    fn name(&self) -> &str {
        &self.name
    }
    
    fn set_name(&mut self, name: String) {
        self.name = name;
    }
    
    fn parent(&self) -> Option<NodeId> {
        self.parent
    }
    
    fn children(&self) -> &[NodeId] {
        &self.children
    }
    
    fn local_transform(&self) -> &Transform2D {
        &self.local_transform
    }
    
    fn local_transform_mut(&mut self) -> &mut Transform2D {
        &mut self.local_transform
    }
    
    fn global_transform(&self, scene: &Scene) -> Transform2D {
        if let Some(parent_id) = self.parent {
            if let Some(parent_node) = scene.get_node(parent_id) {
                let parent_global = parent_node.global_transform(scene);
                parent_global.combine(&self.local_transform)
            } else {
                self.local_transform.clone()
            }
        } else {
            self.local_transform.clone()
        }
    }
    
    fn add_child(&mut self, child_id: NodeId) {
        if !self.children.contains(&child_id) {
            self.children.push(child_id);
        }
    }
    
    fn remove_child(&mut self, child_id: NodeId) {
        self.children.retain(|&id| id != child_id);
    }
    
    fn set_parent(&mut self, parent_id: Option<NodeId>) {
        self.parent = parent_id;
    }
}

/// Trait for nodes that can be rendered
pub trait RenderableNode: Node {
    /// Get the render data for this node
    fn get_render_data(&self) -> RenderData;
}

/// Data needed to render a node
#[derive(Debug, Clone)]
pub struct RenderData {
    pub vertices: Vec<Vertex>,
    pub indices: Vec<u16>,
    pub color: [f32; 4],
}

/// Vertex data for rendering
#[repr(C)]
#[derive(Debug, Clone, Copy, PartialEq, bytemuck::Pod, bytemuck::Zeroable)]
pub struct Vertex {
    pub position: [f32; 2],
}

impl Vertex {
    pub fn desc<'a>() -> wgpu::VertexBufferLayout<'a> {
        wgpu::VertexBufferLayout {
            array_stride: std::mem::size_of::<Vertex>() as wgpu::BufferAddress,
            step_mode: wgpu::VertexStepMode::Vertex,
            attributes: &[
                wgpu::VertexAttribute {
                    offset: 0,
                    shader_location: 0,
                    format: wgpu::VertexFormat::Float32x2,
                },
            ],
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_node_id_generation() {
        let id1 = NodeId::new();
        let id2 = NodeId::new();
        assert_ne!(id1, id2);
        assert!(id1.0 > 0);
        assert!(id2.0 > 0);
    }

    #[test]
    fn test_base_node_creation() {
        let node = BaseNode::new("Test Node".to_string());
        assert_eq!(node.name(), "Test Node");
        assert!(node.parent().is_none());
        assert!(node.children().is_empty());
    }

    #[test]
    fn test_base_node_parent_child_relationships() {
        let mut parent = BaseNode::new("Parent".to_string());
        let mut child = BaseNode::new("Child".to_string());

        let parent_id = parent.id();
        let child_id = child.id();

        // Add child to parent
        parent.add_child(child_id);
        child.set_parent(Some(parent_id));

        assert_eq!(parent.children(), &[child_id]);
        assert_eq!(child.parent(), Some(parent_id));

        // Remove child from parent
        parent.remove_child(child_id);
        child.set_parent(None);

        assert!(parent.children().is_empty());
        assert!(child.parent().is_none());
    }

    #[test]
    fn test_vertex_equality() {
        let v1 = Vertex { position: [1.0, 2.0] };
        let v2 = Vertex { position: [1.0, 2.0] };
        let v3 = Vertex { position: [2.0, 1.0] };

        assert_eq!(v1, v2);
        assert_ne!(v1, v3);
    }

    #[test]
    fn test_render_data_creation() {
        let vertices = vec![
            Vertex { position: [0.0, 1.0] },
            Vertex { position: [-1.0, -1.0] },
            Vertex { position: [1.0, -1.0] },
        ];
        let indices = vec![0, 1, 2];
        let color = [1.0, 0.0, 0.0, 1.0];

        let render_data = RenderData {
            vertices: vertices.clone(),
            indices: indices.clone(),
            color,
        };

        assert_eq!(render_data.vertices, vertices);
        assert_eq!(render_data.indices, indices);
        assert_eq!(render_data.color, color);
    }
}
