use super::*;
use nalgebra::Vector2;

/// Control node - base class for all UI elements (matches <PERSON><PERSON>'s Control)
#[derive(Debug)]
pub struct Control {
    canvas_item: BaseCanvasItem,
    anchor_left: f32,
    anchor_top: f32,
    anchor_right: f32,
    anchor_bottom: f32,
    offset_left: f32,
    offset_top: f32,
    offset_right: f32,
    offset_bottom: f32,
    size: Vector2<f32>,
    position: Vector2<f32>,
    rotation: f32,
    scale: Vector2<f32>,
    pivot_offset: Vector2<f32>,
    clip_contents: bool,
    mouse_filter: MouseFilter,
    focus_mode: FocusMode,
}

impl Control {
    /// Create a new Control
    pub fn new(name: String) -> Self {
        Self {
            canvas_item: BaseCanvasItem::new(name),
            anchor_left: 0.0,
            anchor_top: 0.0,
            anchor_right: 0.0,
            anchor_bottom: 0.0,
            offset_left: 0.0,
            offset_top: 0.0,
            offset_right: 0.0,
            offset_bottom: 0.0,
            size: Vector2::new(0.0, 0.0),
            position: Vector2::new(0.0, 0.0),
            rotation: 0.0,
            scale: Vector2::new(1.0, 1.0),
            pivot_offset: Vector2::new(0.0, 0.0),
            clip_contents: false,
            mouse_filter: MouseFilter::Pass,
            focus_mode: FocusMode::None,
        }
    }
    
    /// Set anchors
    pub fn set_anchors(&mut self, left: f32, top: f32, right: f32, bottom: f32) {
        self.anchor_left = left;
        self.anchor_top = top;
        self.anchor_right = right;
        self.anchor_bottom = bottom;
    }
    
    /// Set size
    pub fn set_size(&mut self, size: Vector2<f32>) {
        self.size = size;
    }
    
    /// Get size
    pub fn size(&self) -> Vector2<f32> {
        self.size
    }
    
    /// Set position
    pub fn set_position(&mut self, position: Vector2<f32>) {
        self.position = position;
    }
    
    /// Get position
    pub fn position(&self) -> Vector2<f32> {
        self.position
    }
    
    /// Get global position
    pub fn global_position(&self) -> Vector2<f32> {
        // In a real implementation, this would calculate based on parent transforms
        self.position
    }
    
    /// Get rect (position + size)
    pub fn get_rect(&self) -> [f32; 4] {
        [self.position.x, self.position.y, self.size.x, self.size.y]
    }
}

/// Mouse filter modes
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum MouseFilter {
    Stop,
    Pass,
    Ignore,
}

/// Focus modes
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum FocusMode {
    None,
    Click,
    All,
}

/// Label node - matches Godot's Label
#[derive(Debug)]
pub struct Label {
    control: Control,
    text: String,
    horizontal_alignment: HorizontalAlignment,
    vertical_alignment: VerticalAlignment,
    autowrap_mode: AutowrapMode,
    clip_text: bool,
    text_direction: TextDirection,
    language: String,
}

impl Label {
    /// Create a new Label
    pub fn new(name: String) -> Self {
        Self {
            control: Control::new(name),
            text: String::new(),
            horizontal_alignment: HorizontalAlignment::Left,
            vertical_alignment: VerticalAlignment::Top,
            autowrap_mode: AutowrapMode::Off,
            clip_text: false,
            text_direction: TextDirection::Auto,
            language: String::new(),
        }
    }
    
    /// Create a label with text
    pub fn with_text(name: String, text: String) -> Self {
        let mut label = Self::new(name);
        label.text = text;
        label
    }
    
    /// Set text
    pub fn set_text(&mut self, text: String) {
        self.text = text;
    }
    
    /// Get text
    pub fn text(&self) -> &str {
        &self.text
    }
    
    /// Set horizontal alignment
    pub fn set_horizontal_alignment(&mut self, alignment: HorizontalAlignment) {
        self.horizontal_alignment = alignment;
    }
    
    /// Set vertical alignment
    pub fn set_vertical_alignment(&mut self, alignment: VerticalAlignment) {
        self.vertical_alignment = alignment;
    }

    /// Set position (delegates to control)
    pub fn set_position(&mut self, position: Vector2<f32>) {
        self.control.set_position(position);
    }

    /// Set size (delegates to control)
    pub fn set_size(&mut self, size: Vector2<f32>) {
        self.control.set_size(size);
    }
}

/// Button node - matches Godot's Button
#[derive(Debug)]
pub struct Button {
    control: Control,
    text: String,
    icon: Option<String>, // In a real implementation, this would be a texture
    flat: bool,
    clip_text: bool,
    alignment: HorizontalAlignment,
    icon_alignment: HorizontalAlignment,
    expand_icon: bool,
    pressed: bool,
    disabled: bool,
    toggle_mode: bool,
    button_pressed: bool,
}

impl Button {
    /// Create a new Button
    pub fn new(name: String) -> Self {
        Self {
            control: Control::new(name),
            text: String::new(),
            icon: None,
            flat: false,
            clip_text: false,
            alignment: HorizontalAlignment::Center,
            icon_alignment: HorizontalAlignment::Left,
            expand_icon: false,
            pressed: false,
            disabled: false,
            toggle_mode: false,
            button_pressed: false,
        }
    }
    
    /// Create a button with text
    pub fn with_text(name: String, text: String) -> Self {
        let mut button = Self::new(name);
        button.text = text;
        button
    }
    
    /// Set text
    pub fn set_text(&mut self, text: String) {
        self.text = text;
    }
    
    /// Get text
    pub fn text(&self) -> &str {
        &self.text
    }
    
    /// Set disabled state
    pub fn set_disabled(&mut self, disabled: bool) {
        self.disabled = disabled;
    }
    
    /// Check if disabled
    pub fn is_disabled(&self) -> bool {
        self.disabled
    }
    
    /// Set pressed state (for toggle buttons)
    pub fn set_pressed(&mut self, pressed: bool) {
        if self.toggle_mode {
            self.button_pressed = pressed;
        }
    }
    
    /// Check if pressed
    pub fn is_pressed(&self) -> bool {
        self.button_pressed
    }
    
    /// Simulate button press
    pub fn press(&mut self) {
        if !self.disabled {
            self.pressed = true;
            // In a real implementation, this would emit a signal
        }
    }
    
    /// Simulate button release
    pub fn release(&mut self) {
        self.pressed = false;
        if self.toggle_mode {
            self.button_pressed = !self.button_pressed;
        }
        // In a real implementation, this would emit a signal
    }

    /// Set position (delegates to control)
    pub fn set_position(&mut self, position: Vector2<f32>) {
        self.control.set_position(position);
    }

    /// Set size (delegates to control)
    pub fn set_size(&mut self, size: Vector2<f32>) {
        self.control.set_size(size);
    }
}

/// Text alignment options
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum HorizontalAlignment {
    Left,
    Center,
    Right,
    Fill,
}

#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum VerticalAlignment {
    Top,
    Center,
    Bottom,
    Fill,
}

/// Autowrap modes
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum AutowrapMode {
    Off,
    Arbitrary,
    Word,
    WordSmart,
}

/// Text direction
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum TextDirection {
    Auto,
    LeftToRight,
    RightToLeft,
}

// Implement Node and CanvasItem for UI nodes
macro_rules! impl_node_for_ui {
    ($type:ty, $field:ident) => {
        impl Node for $type {
            fn id(&self) -> NodeId {
                self.$field.id()
            }
            
            fn name(&self) -> &str {
                self.$field.name()
            }
            
            fn set_name(&mut self, name: String) {
                self.$field.set_name(name);
            }
            
            fn parent(&self) -> Option<NodeId> {
                self.$field.parent()
            }
            
            fn children(&self) -> &[NodeId] {
                self.$field.children()
            }
            
            fn process_mode(&self) -> ProcessMode {
                self.$field.process_mode()
            }
            
            fn set_process_mode(&mut self, mode: ProcessMode) {
                self.$field.set_process_mode(mode);
            }
            
            fn is_inside_tree(&self) -> bool {
                self.$field.is_inside_tree()
            }
            
            fn is_node_ready(&self) -> bool {
                self.$field.is_node_ready()
            }
            
            fn get_groups(&self) -> &[String] {
                self.$field.get_groups()
            }
            
            fn add_to_group(&mut self, group: String) {
                self.$field.add_to_group(group);
            }
            
            fn remove_from_group(&mut self, group: &str) {
                self.$field.remove_from_group(group);
            }
            
            fn is_in_group(&self, group: &str) -> bool {
                self.$field.is_in_group(group)
            }
            
            fn add_child(&mut self, child_id: NodeId) {
                self.$field.add_child(child_id);
            }
            
            fn remove_child(&mut self, child_id: NodeId) {
                self.$field.remove_child(child_id);
            }
            
            fn set_parent(&mut self, parent_id: Option<NodeId>) {
                self.$field.set_parent(parent_id);
            }
            
            fn get_path(&self, scene: &Scene) -> String {
                self.$field.get_path(scene)
            }
            
            fn _enter_tree(&mut self, scene: &mut Scene) {
                self.$field._enter_tree(scene);
            }
            
            fn _ready(&mut self, scene: &mut Scene) {
                self.$field._ready(scene);
            }
            
            fn _exit_tree(&mut self, scene: &mut Scene) {
                self.$field._exit_tree(scene);
            }

            fn as_any(&self) -> &dyn std::any::Any {
                self
            }

            fn as_any_mut(&mut self) -> &mut dyn std::any::Any {
                self
            }
        }
        
        impl CanvasItem for $type {
            fn z_index(&self) -> i32 {
                self.$field.z_index()
            }
            
            fn set_z_index(&mut self, z_index: i32) {
                self.$field.set_z_index(z_index);
            }
            
            fn is_z_relative(&self) -> bool {
                self.$field.is_z_relative()
            }
            
            fn set_z_as_relative(&mut self, relative: bool) {
                self.$field.set_z_as_relative(relative);
            }
            
            fn is_visible(&self) -> bool {
                self.$field.is_visible()
            }
            
            fn set_visible(&mut self, visible: bool) {
                self.$field.set_visible(visible);
            }
            
            fn modulate(&self) -> [f32; 4] {
                self.$field.modulate()
            }
            
            fn set_modulate(&mut self, color: [f32; 4]) {
                self.$field.set_modulate(color);
            }
            
            fn self_modulate(&self) -> [f32; 4] {
                self.$field.self_modulate()
            }
            
            fn set_self_modulate(&mut self, color: [f32; 4]) {
                self.$field.set_self_modulate(color);
            }
            
            fn blend_mode(&self) -> BlendMode {
                self.$field.blend_mode()
            }
            
            fn set_blend_mode(&mut self, mode: BlendMode) {
                self.$field.set_blend_mode(mode);
            }
        }
    };
}

impl_node_for_ui!(Control, canvas_item);
impl_node_for_ui!(Label, control);
impl_node_for_ui!(Button, control);
