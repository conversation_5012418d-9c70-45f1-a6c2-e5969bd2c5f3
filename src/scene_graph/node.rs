use super::*;
use nalgebra::Vector2;

/// Base implementation of a scene graph node - matches <PERSON><PERSON>'s Node class
#[derive(Debug)]
pub struct BaseNode {
    id: NodeId,
    name: String,
    parent: Option<NodeId>,
    children: Vec<NodeId>,
    process_mode: ProcessMode,
    groups: Vec<String>,
    inside_tree: bool,
    ready: bool,
    process_flags: ProcessFlags,
}

impl BaseNode {
    /// Create a new base node with the given name
    pub fn new(name: String) -> Self {
        Self {
            id: NodeId::new(),
            name,
            parent: None,
            children: Vec::new(),
            process_mode: ProcessMode::default(),
            groups: Vec::new(),
            inside_tree: false,
            ready: false,
            process_flags: ProcessFlags::default(),
        }
    }

    /// Set whether this node should process
    pub fn set_process(&mut self, enabled: bool) {
        self.process_flags.process = enabled;
    }

    /// Set whether this node should physics process
    pub fn set_physics_process(&mut self, enabled: bool) {
        self.process_flags.physics_process = enabled;
    }

    /// Check if this node should process
    pub fn is_processing(&self) -> bool {
        self.process_flags.process
    }

    /// Check if this node should physics process
    pub fn is_physics_processing(&self) -> bool {
        self.process_flags.physics_process
    }
}

impl Node for BaseNode {
    fn id(&self) -> NodeId {
        self.id
    }

    fn name(&self) -> &str {
        &self.name
    }

    fn set_name(&mut self, name: String) {
        self.name = name;
    }

    fn parent(&self) -> Option<NodeId> {
        self.parent
    }

    fn children(&self) -> &[NodeId] {
        &self.children
    }

    fn process_mode(&self) -> ProcessMode {
        self.process_mode
    }

    fn set_process_mode(&mut self, mode: ProcessMode) {
        self.process_mode = mode;
    }

    fn is_inside_tree(&self) -> bool {
        self.inside_tree
    }

    fn is_node_ready(&self) -> bool {
        self.ready
    }

    fn get_groups(&self) -> &[String] {
        &self.groups
    }

    fn add_to_group(&mut self, group: String) {
        if !self.groups.contains(&group) {
            self.groups.push(group);
        }
    }

    fn remove_from_group(&mut self, group: &str) {
        self.groups.retain(|g| g != group);
    }

    fn is_in_group(&self, group: &str) -> bool {
        self.groups.iter().any(|g| g == group)
    }

    fn add_child(&mut self, child_id: NodeId) {
        if !self.children.contains(&child_id) {
            self.children.push(child_id);
        }
    }

    fn remove_child(&mut self, child_id: NodeId) {
        self.children.retain(|&id| id != child_id);
    }

    fn set_parent(&mut self, parent_id: Option<NodeId>) {
        self.parent = parent_id;
    }

    fn get_path(&self, scene: &Scene) -> String {
        if let Some(parent_id) = self.parent {
            if let Some(parent) = scene.get_node(parent_id) {
                format!("{}/{}", parent.get_path(scene), self.name)
            } else {
                format!("/{}", self.name)
            }
        } else {
            format!("/{}", self.name)
        }
    }

    fn _enter_tree(&mut self, _scene: &mut Scene) {
        self.inside_tree = true;
    }

    fn _ready(&mut self, _scene: &mut Scene) {
        self.ready = true;
    }

    fn _exit_tree(&mut self, _scene: &mut Scene) {
        self.inside_tree = false;
        self.ready = false;
    }
}

/// Sprite2D node - matches Godot's Sprite2D class
#[derive(Debug)]
pub struct Sprite2D {
    node2d: BaseNode2D,
    texture_size: Vector2<f32>,
    region_enabled: bool,
    region_rect: [f32; 4], // x, y, width, height
    flip_h: bool,
    flip_v: bool,
    centered: bool,
    offset: Vector2<f32>,
}

impl Sprite2D {
    /// Create a new Sprite2D
    pub fn new(name: String) -> Self {
        Self {
            node2d: BaseNode2D::new(name),
            texture_size: Vector2::new(1.0, 1.0),
            region_enabled: false,
            region_rect: [0.0, 0.0, 1.0, 1.0],
            flip_h: false,
            flip_v: false,
            centered: true,
            offset: Vector2::new(0.0, 0.0),
        }
    }

    /// Create a triangle sprite (for testing)
    pub fn triangle(name: String, size: f32, color: [f32; 4]) -> Self {
        let mut sprite = Self::new(name);
        sprite.texture_size = Vector2::new(size, size);
        sprite.node2d.canvas_item_mut().set_self_modulate(color);
        sprite
    }

    /// Create a triangle sprite with position
    pub fn triangle_with_position(name: String, position: Vector2<f32>, size: f32, color: [f32; 4]) -> Self {
        let mut sprite = Self::triangle(name, size, color);
        sprite.node2d.transform_mut().set_position(position);
        sprite
    }

    /// Get render data for this sprite
    pub fn get_render_data(&self) -> RenderData {
        let size = self.texture_size.x * 0.5; // Use half size for triangle
        let vertices = vec![
            Vertex { position: [0.0, size * 0.5] },      // Top
            Vertex { position: [-size * 0.5, -size * 0.5] }, // Bottom-left
            Vertex { position: [size * 0.5, -size * 0.5] },  // Bottom-right
        ];

        let indices = vec![0, 1, 2];
        let color = self.node2d.canvas_item().self_modulate();

        RenderData {
            vertices,
            indices,
            color,
        }
    }
}

impl Node for Sprite2D {
    fn id(&self) -> NodeId {
        self.node2d.id()
    }

    fn name(&self) -> &str {
        self.node2d.name()
    }

    fn set_name(&mut self, name: String) {
        self.node2d.set_name(name);
    }

    fn parent(&self) -> Option<NodeId> {
        self.node2d.parent()
    }

    fn children(&self) -> &[NodeId] {
        self.node2d.children()
    }

    fn process_mode(&self) -> ProcessMode {
        self.node2d.process_mode()
    }

    fn set_process_mode(&mut self, mode: ProcessMode) {
        self.node2d.set_process_mode(mode);
    }

    fn is_inside_tree(&self) -> bool {
        self.node2d.is_inside_tree()
    }

    fn is_node_ready(&self) -> bool {
        self.node2d.is_node_ready()
    }

    fn get_groups(&self) -> &[String] {
        self.node2d.get_groups()
    }

    fn add_to_group(&mut self, group: String) {
        self.node2d.add_to_group(group);
    }

    fn remove_from_group(&mut self, group: &str) {
        self.node2d.remove_from_group(group);
    }

    fn is_in_group(&self, group: &str) -> bool {
        self.node2d.is_in_group(group)
    }

    fn add_child(&mut self, child_id: NodeId) {
        self.node2d.add_child(child_id);
    }

    fn remove_child(&mut self, child_id: NodeId) {
        self.node2d.remove_child(child_id);
    }

    fn set_parent(&mut self, parent_id: Option<NodeId>) {
        self.node2d.set_parent(parent_id);
    }

    fn get_path(&self, scene: &Scene) -> String {
        self.node2d.get_path(scene)
    }

    fn _enter_tree(&mut self, scene: &mut Scene) {
        self.node2d._enter_tree(scene);
    }

    fn _ready(&mut self, scene: &mut Scene) {
        self.node2d._ready(scene);
    }

    fn _exit_tree(&mut self, scene: &mut Scene) {
        self.node2d._exit_tree(scene);
    }
}

impl CanvasItem for Sprite2D {
    fn z_index(&self) -> i32 {
        self.node2d.z_index()
    }

    fn set_z_index(&mut self, z_index: i32) {
        self.node2d.set_z_index(z_index);
    }

    fn is_z_relative(&self) -> bool {
        self.node2d.is_z_relative()
    }

    fn set_z_as_relative(&mut self, relative: bool) {
        self.node2d.set_z_as_relative(relative);
    }

    fn is_visible(&self) -> bool {
        self.node2d.is_visible()
    }

    fn set_visible(&mut self, visible: bool) {
        self.node2d.set_visible(visible);
    }

    fn modulate(&self) -> [f32; 4] {
        self.node2d.modulate()
    }

    fn set_modulate(&mut self, color: [f32; 4]) {
        self.node2d.set_modulate(color);
    }

    fn self_modulate(&self) -> [f32; 4] {
        self.node2d.self_modulate()
    }

    fn set_self_modulate(&mut self, color: [f32; 4]) {
        self.node2d.set_self_modulate(color);
    }

    fn blend_mode(&self) -> BlendMode {
        self.node2d.blend_mode()
    }

    fn set_blend_mode(&mut self, mode: BlendMode) {
        self.node2d.set_blend_mode(mode);
    }

    fn _draw(&self, render_context: &RenderContext) {
        self.node2d._draw(render_context);
    }
}

impl Node2D for Sprite2D {
    fn transform(&self) -> &Transform2D {
        self.node2d.transform()
    }

    fn transform_mut(&mut self) -> &mut Transform2D {
        self.node2d.transform_mut()
    }

    fn global_transform(&self, scene: &Scene) -> Transform2D {
        self.node2d.global_transform(scene)
    }
}

impl Renderable for Sprite2D {
    fn get_render_data(&self) -> RenderData {
        self.get_render_data()
    }

    fn should_render(&self) -> bool {
        self.is_visible() && self.is_inside_tree()
    }
}


