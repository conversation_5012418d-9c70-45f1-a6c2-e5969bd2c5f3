use super::*;
use nalgebra::Vector2;

/// A 2D node that can be positioned, rotated, and scaled
#[derive(Debug)]
pub struct Node2D {
    base: BaseNode,
}

impl Node2D {
    /// Create a new Node2D with the given name
    pub fn new(name: String) -> Self {
        Self {
            base: BaseNode::new(name),
        }
    }
    
    /// Create a new Node2D with the given name and position
    pub fn with_position(name: String, position: Vector2<f32>) -> Self {
        let mut node = Self::new(name);
        node.base.local_transform.set_position(position);
        node
    }
}

impl Node for Node2D {
    fn id(&self) -> NodeId {
        self.base.id()
    }
    
    fn name(&self) -> &str {
        self.base.name()
    }
    
    fn set_name(&mut self, name: String) {
        self.base.set_name(name);
    }
    
    fn parent(&self) -> Option<NodeId> {
        self.base.parent()
    }
    
    fn children(&self) -> &[NodeId] {
        self.base.children()
    }
    
    fn local_transform(&self) -> &Transform2D {
        self.base.local_transform()
    }
    
    fn local_transform_mut(&mut self) -> &mut Transform2D {
        self.base.local_transform_mut()
    }
    
    fn global_transform(&self, scene: &Scene) -> Transform2D {
        self.base.global_transform(scene)
    }
    
    fn add_child(&mut self, child_id: NodeId) {
        self.base.add_child(child_id);
    }
    
    fn remove_child(&mut self, child_id: NodeId) {
        self.base.remove_child(child_id);
    }
    
    fn set_parent(&mut self, parent_id: Option<NodeId>) {
        self.base.set_parent(parent_id);
    }
}

/// A renderable node that can display graphics
#[derive(Debug)]
pub struct RenderableNode2D {
    base: Node2D,
    render_data: RenderData,
    visible: bool,
}

impl RenderableNode2D {
    /// Create a new renderable node with the given name and render data
    pub fn new(name: String, render_data: RenderData) -> Self {
        Self {
            base: Node2D::new(name),
            render_data,
            visible: true,
        }
    }
    
    /// Create a triangle renderable node
    pub fn triangle(name: String, size: f32, color: [f32; 4]) -> Self {
        let vertices = vec![
            Vertex { position: [0.0, size * 0.5] },      // Top
            Vertex { position: [-size * 0.5, -size * 0.5] }, // Bottom-left
            Vertex { position: [size * 0.5, -size * 0.5] },  // Bottom-right
        ];
        
        let indices = vec![0, 1, 2];
        
        let render_data = RenderData {
            vertices,
            indices,
            color,
        };
        
        Self::new(name, render_data)
    }
    
    /// Create a triangle renderable node with position
    pub fn triangle_with_position(name: String, position: Vector2<f32>, size: f32, color: [f32; 4]) -> Self {
        let mut node = Self::triangle(name, size, color);
        node.base.local_transform_mut().set_position(position);
        node
    }
    
    /// Set the visibility of this node
    pub fn set_visible(&mut self, visible: bool) {
        self.visible = visible;
    }
    
    /// Check if this node is visible
    pub fn is_visible(&self) -> bool {
        self.visible
    }
    
    /// Update the render data
    pub fn set_render_data(&mut self, render_data: RenderData) {
        self.render_data = render_data;
    }
}

impl Node for RenderableNode2D {
    fn id(&self) -> NodeId {
        self.base.id()
    }
    
    fn name(&self) -> &str {
        self.base.name()
    }
    
    fn set_name(&mut self, name: String) {
        self.base.set_name(name);
    }
    
    fn parent(&self) -> Option<NodeId> {
        self.base.parent()
    }
    
    fn children(&self) -> &[NodeId] {
        self.base.children()
    }
    
    fn local_transform(&self) -> &Transform2D {
        self.base.local_transform()
    }
    
    fn local_transform_mut(&mut self) -> &mut Transform2D {
        self.base.local_transform_mut()
    }
    
    fn global_transform(&self, scene: &Scene) -> Transform2D {
        self.base.global_transform(scene)
    }
    
    fn add_child(&mut self, child_id: NodeId) {
        self.base.add_child(child_id);
    }
    
    fn remove_child(&mut self, child_id: NodeId) {
        self.base.remove_child(child_id);
    }
    
    fn set_parent(&mut self, parent_id: Option<NodeId>) {
        self.base.set_parent(parent_id);
    }
    
    fn is_renderable(&self) -> bool {
        self.visible
    }
    
    fn render(&self, _render_context: &RenderContext) {
        if !self.visible {
            return;
        }
        
        // This will be implemented when we update the rendering system
        // For now, this is a placeholder
    }
}

impl RenderableNode for RenderableNode2D {
    fn get_render_data(&self) -> RenderData {
        self.render_data.clone()
    }
}

/// A physics-enabled node (placeholder for future physics integration)
#[derive(Debug)]
pub struct PhysicsNode2D {
    base: Node2D,
    // Physics properties will be added here when integrating with physics engine
}

impl PhysicsNode2D {
    /// Create a new physics node with the given name
    pub fn new(name: String) -> Self {
        Self {
            base: Node2D::new(name),
        }
    }
}

impl Node for PhysicsNode2D {
    fn id(&self) -> NodeId {
        self.base.id()
    }
    
    fn name(&self) -> &str {
        self.base.name()
    }
    
    fn set_name(&mut self, name: String) {
        self.base.set_name(name);
    }
    
    fn parent(&self) -> Option<NodeId> {
        self.base.parent()
    }
    
    fn children(&self) -> &[NodeId] {
        self.base.children()
    }
    
    fn local_transform(&self) -> &Transform2D {
        self.base.local_transform()
    }
    
    fn local_transform_mut(&mut self) -> &mut Transform2D {
        self.base.local_transform_mut()
    }
    
    fn global_transform(&self, scene: &Scene) -> Transform2D {
        self.base.global_transform(scene)
    }
    
    fn add_child(&mut self, child_id: NodeId) {
        self.base.add_child(child_id);
    }
    
    fn remove_child(&mut self, child_id: NodeId) {
        self.base.remove_child(child_id);
    }
    
    fn set_parent(&mut self, parent_id: Option<NodeId>) {
        self.base.set_parent(parent_id);
    }
}
