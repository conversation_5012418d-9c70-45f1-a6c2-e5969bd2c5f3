use super::*;
use nalgebra::Vector2;

/// A 2D node that can be positioned, rotated, and scaled
#[derive(Debug)]
pub struct Node2D {
    base: BaseNode,
}

impl Node2D {
    /// Create a new Node2D with the given name
    pub fn new(name: String) -> Self {
        Self {
            base: BaseNode::new(name),
        }
    }
    
    /// Create a new Node2D with the given name and position
    pub fn with_position(name: String, position: Vector2<f32>) -> Self {
        let mut node = Self::new(name);
        node.base.local_transform.set_position(position);
        node
    }
}

impl Node for Node2D {
    fn id(&self) -> NodeId {
        self.base.id()
    }
    
    fn name(&self) -> &str {
        self.base.name()
    }
    
    fn set_name(&mut self, name: String) {
        self.base.set_name(name);
    }
    
    fn parent(&self) -> Option<NodeId> {
        self.base.parent()
    }
    
    fn children(&self) -> &[NodeId] {
        self.base.children()
    }
    
    fn local_transform(&self) -> &Transform2D {
        self.base.local_transform()
    }
    
    fn local_transform_mut(&mut self) -> &mut Transform2D {
        self.base.local_transform_mut()
    }
    
    fn global_transform(&self, scene: &Scene) -> Transform2D {
        self.base.global_transform(scene)
    }
    
    fn add_child(&mut self, child_id: NodeId) {
        self.base.add_child(child_id);
    }
    
    fn remove_child(&mut self, child_id: NodeId) {
        self.base.remove_child(child_id);
    }
    
    fn set_parent(&mut self, parent_id: Option<NodeId>) {
        self.base.set_parent(parent_id);
    }
}

/// A renderable node that can display graphics
#[derive(Debug)]
pub struct RenderableNode2D {
    base: Node2D,
    render_data: RenderData,
    visible: bool,
}

impl RenderableNode2D {
    /// Create a new renderable node with the given name and render data
    pub fn new(name: String, render_data: RenderData) -> Self {
        Self {
            base: Node2D::new(name),
            render_data,
            visible: true,
        }
    }
    
    /// Create a triangle renderable node
    pub fn triangle(name: String, size: f32, color: [f32; 4]) -> Self {
        let vertices = vec![
            Vertex { position: [0.0, size * 0.5] },      // Top
            Vertex { position: [-size * 0.5, -size * 0.5] }, // Bottom-left
            Vertex { position: [size * 0.5, -size * 0.5] },  // Bottom-right
        ];
        
        let indices = vec![0, 1, 2];
        
        let render_data = RenderData {
            vertices,
            indices,
            color,
        };
        
        Self::new(name, render_data)
    }
    
    /// Create a triangle renderable node with position
    pub fn triangle_with_position(name: String, position: Vector2<f32>, size: f32, color: [f32; 4]) -> Self {
        let mut node = Self::triangle(name, size, color);
        node.base.local_transform_mut().set_position(position);
        node
    }
    
    /// Set the visibility of this node
    pub fn set_visible(&mut self, visible: bool) {
        self.visible = visible;
    }
    
    /// Check if this node is visible
    pub fn is_visible(&self) -> bool {
        self.visible
    }
    
    /// Update the render data
    pub fn set_render_data(&mut self, render_data: RenderData) {
        self.render_data = render_data;
    }
}

impl Node for RenderableNode2D {
    fn id(&self) -> NodeId {
        self.base.id()
    }
    
    fn name(&self) -> &str {
        self.base.name()
    }
    
    fn set_name(&mut self, name: String) {
        self.base.set_name(name);
    }
    
    fn parent(&self) -> Option<NodeId> {
        self.base.parent()
    }
    
    fn children(&self) -> &[NodeId] {
        self.base.children()
    }
    
    fn local_transform(&self) -> &Transform2D {
        self.base.local_transform()
    }
    
    fn local_transform_mut(&mut self) -> &mut Transform2D {
        self.base.local_transform_mut()
    }
    
    fn global_transform(&self, scene: &Scene) -> Transform2D {
        self.base.global_transform(scene)
    }
    
    fn add_child(&mut self, child_id: NodeId) {
        self.base.add_child(child_id);
    }
    
    fn remove_child(&mut self, child_id: NodeId) {
        self.base.remove_child(child_id);
    }
    
    fn set_parent(&mut self, parent_id: Option<NodeId>) {
        self.base.set_parent(parent_id);
    }
    
    fn is_renderable(&self) -> bool {
        self.visible
    }
    
    fn render(&self, _render_context: &RenderContext) {
        if !self.visible {
            return;
        }
        
        // This will be implemented when we update the rendering system
        // For now, this is a placeholder
    }
}

impl RenderableNode for RenderableNode2D {
    fn get_render_data(&self) -> RenderData {
        self.render_data.clone()
    }
}

/// A physics-enabled node (placeholder for future physics integration)
#[derive(Debug)]
pub struct PhysicsNode2D {
    base: Node2D,
    // Physics properties will be added here when integrating with physics engine
}

impl PhysicsNode2D {
    /// Create a new physics node with the given name
    pub fn new(name: String) -> Self {
        Self {
            base: Node2D::new(name),
        }
    }
}

impl Node for PhysicsNode2D {
    fn id(&self) -> NodeId {
        self.base.id()
    }
    
    fn name(&self) -> &str {
        self.base.name()
    }
    
    fn set_name(&mut self, name: String) {
        self.base.set_name(name);
    }
    
    fn parent(&self) -> Option<NodeId> {
        self.base.parent()
    }
    
    fn children(&self) -> &[NodeId] {
        self.base.children()
    }
    
    fn local_transform(&self) -> &Transform2D {
        self.base.local_transform()
    }
    
    fn local_transform_mut(&mut self) -> &mut Transform2D {
        self.base.local_transform_mut()
    }
    
    fn global_transform(&self, scene: &Scene) -> Transform2D {
        self.base.global_transform(scene)
    }
    
    fn add_child(&mut self, child_id: NodeId) {
        self.base.add_child(child_id);
    }
    
    fn remove_child(&mut self, child_id: NodeId) {
        self.base.remove_child(child_id);
    }
    
    fn set_parent(&mut self, parent_id: Option<NodeId>) {
        self.base.set_parent(parent_id);
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use nalgebra::Vector2;

    #[test]
    fn test_node2d_creation() {
        let node = Node2D::new("Test Node".to_string());
        assert_eq!(node.name(), "Test Node");
        assert!(node.parent().is_none());
        assert!(node.children().is_empty());
        assert_eq!(node.local_transform().position, Vector2::new(0.0, 0.0));
    }

    #[test]
    fn test_node2d_with_position() {
        let position = Vector2::new(10.0, 20.0);
        let node = Node2D::with_position("Positioned Node".to_string(), position);
        assert_eq!(node.name(), "Positioned Node");
        assert_eq!(node.local_transform().position, position);
    }

    #[test]
    fn test_renderable_node2d_triangle() {
        let node = RenderableNode2D::triangle("Triangle".to_string(), 1.0, [1.0, 0.0, 0.0, 1.0]);
        assert_eq!(node.name(), "Triangle");
        assert!(node.is_visible());
        assert!(node.is_renderable());

        let render_data = node.get_render_data();
        assert_eq!(render_data.vertices.len(), 3);
        assert_eq!(render_data.indices.len(), 3);
        assert_eq!(render_data.color, [1.0, 0.0, 0.0, 1.0]);
    }

    #[test]
    fn test_renderable_node2d_triangle_with_position() {
        let position = Vector2::new(5.0, 10.0);
        let node = RenderableNode2D::triangle_with_position(
            "Positioned Triangle".to_string(),
            position,
            2.0,
            [0.0, 1.0, 0.0, 1.0]
        );

        assert_eq!(node.name(), "Positioned Triangle");
        assert_eq!(node.local_transform().position, position);
        assert!(node.is_visible());

        let render_data = node.get_render_data();
        assert_eq!(render_data.color, [0.0, 1.0, 0.0, 1.0]);
    }

    #[test]
    fn test_renderable_node2d_visibility() {
        let mut node = RenderableNode2D::triangle("Triangle".to_string(), 1.0, [1.0, 0.0, 0.0, 1.0]);

        assert!(node.is_visible());
        assert!(node.is_renderable());

        node.set_visible(false);
        assert!(!node.is_visible());
        assert!(!node.is_renderable());

        node.set_visible(true);
        assert!(node.is_visible());
        assert!(node.is_renderable());
    }

    #[test]
    fn test_renderable_node2d_render_data_update() {
        let mut node = RenderableNode2D::triangle("Triangle".to_string(), 1.0, [1.0, 0.0, 0.0, 1.0]);

        let new_render_data = RenderData {
            vertices: vec![
                Vertex { position: [0.0, 2.0] },
                Vertex { position: [-2.0, -2.0] },
                Vertex { position: [2.0, -2.0] },
            ],
            indices: vec![0, 1, 2],
            color: [0.0, 0.0, 1.0, 1.0],
        };

        node.set_render_data(new_render_data.clone());
        let retrieved_data = node.get_render_data();

        assert_eq!(retrieved_data.vertices, new_render_data.vertices);
        assert_eq!(retrieved_data.indices, new_render_data.indices);
        assert_eq!(retrieved_data.color, new_render_data.color);
    }

    #[test]
    fn test_physics_node2d_creation() {
        let node = PhysicsNode2D::new("Physics Node".to_string());
        assert_eq!(node.name(), "Physics Node");
        assert!(node.parent().is_none());
        assert!(node.children().is_empty());
        assert!(!node.is_renderable()); // Physics nodes are not renderable by default
    }

    #[test]
    fn test_node_hierarchy() {
        let mut parent = Node2D::new("Parent".to_string());
        let mut child1 = Node2D::new("Child1".to_string());
        let mut child2 = Node2D::new("Child2".to_string());

        let parent_id = parent.id();
        let child1_id = child1.id();
        let child2_id = child2.id();

        // Build hierarchy
        parent.add_child(child1_id);
        parent.add_child(child2_id);
        child1.set_parent(Some(parent_id));
        child2.set_parent(Some(parent_id));

        // Verify relationships
        assert_eq!(parent.children().len(), 2);
        assert!(parent.children().contains(&child1_id));
        assert!(parent.children().contains(&child2_id));
        assert_eq!(child1.parent(), Some(parent_id));
        assert_eq!(child2.parent(), Some(parent_id));

        // Remove one child
        parent.remove_child(child1_id);
        child1.set_parent(None);

        assert_eq!(parent.children().len(), 1);
        assert!(!parent.children().contains(&child1_id));
        assert!(parent.children().contains(&child2_id));
        assert_eq!(child1.parent(), None);
        assert_eq!(child2.parent(), Some(parent_id));
    }

    #[test]
    fn test_node_transform_modification() {
        let mut node = Node2D::new("Test Node".to_string());

        // Modify transform
        let new_position = Vector2::new(100.0, 200.0);
        node.local_transform_mut().set_position(new_position);
        node.local_transform_mut().set_rotation(std::f32::consts::PI / 4.0);
        node.local_transform_mut().set_scale(Vector2::new(2.0, 3.0));

        // Verify changes
        assert_eq!(node.local_transform().position, new_position);
        assert_eq!(node.local_transform().rotation, std::f32::consts::PI / 4.0);
        assert_eq!(node.local_transform().scale, Vector2::new(2.0, 3.0));
    }
}
