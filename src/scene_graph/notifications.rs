/// Godot-style notification constants
#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Eq)]
pub enum Notification {
    /// Node entered the scene tree
    EnterTree = 10,
    /// Node is ready (after _enter_tree and all children are ready)
    Ready = 13,
    /// <PERSON>de exited the scene tree
    ExitTree = 11,
    /// Node should be processed
    Process = 17,
    /// Node should be physics processed
    PhysicsProcess = 16,
    /// Node should be paused
    Paused = 14,
    /// Node should be unpaused
    Unpaused = 15,
    /// Node's transform changed
    TransformChanged = 2000,
    /// Node's local transform changed
    LocalTransformChanged = 35,
    /// Node's visibility changed
    VisibilityChanged = 43,
    /// Node should be drawn
    Draw = 30,
    /// Node's z_index changed
    ZIndexChanged = 2001,
    /// Custom notification start
    Custom = 1000,
}

impl From<i32> for Notification {
    fn from(value: i32) -> Self {
        match value {
            10 => Notification::EnterTree,
            11 => Notification::ExitTree,
            13 => Notification::Ready,
            14 => Notification::Paused,
            15 => Notification::Unpaused,
            16 => Notification::PhysicsProcess,
            17 => Notification::Process,
            30 => Notification::Draw,
            35 => Notification::LocalTransformChanged,
            43 => Notification::VisibilityChanged,
            2000 => Notification::TransformChanged,
            2001 => Notification::ZIndexChanged,
            _ => Notification::Custom,
        }
    }
}

impl From<Notification> for i32 {
    fn from(notification: Notification) -> Self {
        notification as i32
    }
}

/// Node processing flags
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub struct ProcessFlags {
    pub process: bool,
    pub physics_process: bool,
    pub input: bool,
    pub unhandled_input: bool,
    pub internal_process: bool,
    pub internal_physics_process: bool,
}

impl Default for ProcessFlags {
    fn default() -> Self {
        Self {
            process: false,
            physics_process: false,
            input: false,
            unhandled_input: false,
            internal_process: false,
            internal_physics_process: false,
        }
    }
}

impl ProcessFlags {
    pub fn new() -> Self {
        Self::default()
    }
    
    pub fn with_process(mut self, enabled: bool) -> Self {
        self.process = enabled;
        self
    }
    
    pub fn with_physics_process(mut self, enabled: bool) -> Self {
        self.physics_process = enabled;
        self
    }
    
    pub fn with_input(mut self, enabled: bool) -> Self {
        self.input = enabled;
        self
    }
    
    pub fn with_unhandled_input(mut self, enabled: bool) -> Self {
        self.unhandled_input = enabled;
        self
    }
}
