use super::*;
use std::collections::HashMap;
use std::any::Any;

/// Signal system - matches <PERSON><PERSON>'s signal functionality
pub struct SignalSystem {
    connections: HashMap<SignalId, Vec<Connection>>,
    signal_counter: u64,
}

impl SignalSystem {
    /// Create a new signal system
    pub fn new() -> Self {
        Self {
            connections: HashMap::new(),
            signal_counter: 0,
        }
    }
    
    /// Create a new signal
    pub fn create_signal(&mut self, name: String) -> SignalId {
        let id = SignalId {
            id: self.signal_counter,
            name,
        };
        self.signal_counter += 1;
        self.connections.insert(id.clone(), Vec::new());
        id
    }
    
    /// Connect a signal to a callable
    pub fn connect(&mut self, signal_id: SignalId, target: NodeId, method: String, flags: ConnectFlags) -> Result<(), SignalError> {
        if let Some(connections) = self.connections.get_mut(&signal_id) {
            // Check if already connected (unless REFERENCE_COUNTED is set)
            if !flags.contains(ConnectFlags::REFERENCE_COUNTED) {
                if connections.iter().any(|c| c.target == target && c.method == method) {
                    return Err(SignalError::AlreadyConnected);
                }
            }

            let connection = Connection {
                target,
                method,
                flags,
                one_shot: flags.contains(ConnectFlags::ONE_SHOT),
            };

            connections.push(connection);
            Ok(())
        } else {
            Err(SignalError::SignalNotFound)
        }
    }
    
    /// Disconnect a signal
    pub fn disconnect(&mut self, signal_id: SignalId, target: NodeId, method: String) -> Result<(), SignalError> {
        if let Some(connections) = self.connections.get_mut(&signal_id) {
            let initial_len = connections.len();
            connections.retain(|c| !(c.target == target && c.method == method));
            
            if connections.len() < initial_len {
                Ok(())
            } else {
                Err(SignalError::NotConnected)
            }
        } else {
            Err(SignalError::SignalNotFound)
        }
    }
    
    /// Emit a signal
    pub fn emit(&mut self, signal_id: SignalId, args: Vec<Box<dyn Any>>) -> Result<(), SignalError> {
        if let Some(connections) = self.connections.get_mut(&signal_id) {
            let mut to_remove = Vec::new();
            
            for (index, connection) in connections.iter().enumerate() {
                // In a real implementation, we would call the method on the target node
                // For now, we'll just print the signal emission
                println!("Signal '{}' emitted to node {:?}, method '{}'", 
                    signal_id.name, connection.target, connection.method);
                
                // Mark one-shot connections for removal
                if connection.one_shot {
                    to_remove.push(index);
                }
            }
            
            // Remove one-shot connections (in reverse order to maintain indices)
            for &index in to_remove.iter().rev() {
                connections.remove(index);
            }
            
            Ok(())
        } else {
            Err(SignalError::SignalNotFound)
        }
    }
    
    /// Check if a signal is connected
    pub fn is_connected(&self, signal_id: &SignalId, target: NodeId, method: &str) -> bool {
        if let Some(connections) = self.connections.get(signal_id) {
            connections.iter().any(|c| c.target == target && c.method == method)
        } else {
            false
        }
    }
    
    /// Get all connections for a signal
    pub fn get_connections(&self, signal_id: &SignalId) -> Option<&Vec<Connection>> {
        self.connections.get(signal_id)
    }
    
    /// Disconnect all connections for a node
    pub fn disconnect_all_for_node(&mut self, node_id: NodeId) {
        for connections in self.connections.values_mut() {
            connections.retain(|c| c.target != node_id);
        }
    }
}

impl Default for SignalSystem {
    fn default() -> Self {
        Self::new()
    }
}

/// Signal identifier
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub struct SignalId {
    pub id: u64,
    pub name: String,
}

/// Signal connection
#[derive(Debug, Clone)]
pub struct Connection {
    pub target: NodeId,
    pub method: String,
    pub flags: ConnectFlags,
    pub one_shot: bool,
}

/// Connection flags - matches Godot's connect flags
bitflags::bitflags! {
    #[derive(Debug, Clone, Copy, PartialEq, Eq)]
    pub struct ConnectFlags: u32 {
        /// Default connection
        const NONE = 0;
        /// Connection will be disconnected after first emission
        const ONE_SHOT = 1;
        /// Connection can be made multiple times
        const REFERENCE_COUNTED = 2;
        /// Connection persists across scene changes
        const PERSIST = 4;
    }
}

impl Default for ConnectFlags {
    fn default() -> Self {
        ConnectFlags::NONE
    }
}

/// Signal errors
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum SignalError {
    SignalNotFound,
    AlreadyConnected,
    NotConnected,
    InvalidTarget,
    InvalidMethod,
}

impl std::fmt::Display for SignalError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            SignalError::SignalNotFound => write!(f, "Signal not found"),
            SignalError::AlreadyConnected => write!(f, "Signal already connected"),
            SignalError::NotConnected => write!(f, "Signal not connected"),
            SignalError::InvalidTarget => write!(f, "Invalid target node"),
            SignalError::InvalidMethod => write!(f, "Invalid method name"),
        }
    }
}

impl std::error::Error for SignalError {}

/// Trait for nodes that can emit signals
pub trait SignalEmitter {
    /// Get the signal system
    fn signal_system(&self) -> &SignalSystem;
    
    /// Get the signal system mutably
    fn signal_system_mut(&mut self) -> &mut SignalSystem;
    
    /// Define a signal
    fn add_user_signal(&mut self, name: String) -> SignalId {
        self.signal_system_mut().create_signal(name)
    }
    
    /// Connect a signal
    fn connect_signal(&mut self, signal_id: SignalId, target: NodeId, method: String, flags: ConnectFlags) -> Result<(), SignalError> {
        self.signal_system_mut().connect(signal_id, target, method, flags)
    }
    
    /// Disconnect a signal
    fn disconnect_signal(&mut self, signal_id: SignalId, target: NodeId, method: String) -> Result<(), SignalError> {
        self.signal_system_mut().disconnect(signal_id, target, method)
    }
    
    /// Emit a signal
    fn emit_signal(&mut self, signal_id: SignalId, args: Vec<Box<dyn Any>>) -> Result<(), SignalError> {
        self.signal_system_mut().emit(signal_id, args)
    }
    
    /// Check if signal is connected
    fn is_signal_connected(&self, signal_id: &SignalId, target: NodeId, method: &str) -> bool {
        self.signal_system().is_connected(signal_id, target, method)
    }
}

/// Common signals that many nodes emit
pub struct CommonSignals {
    pub tree_entered: SignalId,
    pub tree_exiting: SignalId,
    pub tree_exited: SignalId,
    pub ready: SignalId,
    pub renamed: SignalId,
}

impl CommonSignals {
    /// Create common signals
    pub fn new(signal_system: &mut SignalSystem) -> Self {
        Self {
            tree_entered: signal_system.create_signal("tree_entered".to_string()),
            tree_exiting: signal_system.create_signal("tree_exiting".to_string()),
            tree_exited: signal_system.create_signal("tree_exited".to_string()),
            ready: signal_system.create_signal("ready".to_string()),
            renamed: signal_system.create_signal("renamed".to_string()),
        }
    }
}

/// Group system for organizing nodes
#[derive(Debug, Default)]
pub struct GroupSystem {
    groups: HashMap<String, Vec<NodeId>>,
}

impl GroupSystem {
    /// Create a new group system
    pub fn new() -> Self {
        Self {
            groups: HashMap::new(),
        }
    }
    
    /// Add a node to a group
    pub fn add_to_group(&mut self, node_id: NodeId, group: String) {
        let nodes = self.groups.entry(group).or_insert_with(Vec::new);
        if !nodes.contains(&node_id) {
            nodes.push(node_id);
        }
    }
    
    /// Remove a node from a group
    pub fn remove_from_group(&mut self, node_id: NodeId, group: &str) {
        if let Some(nodes) = self.groups.get_mut(group) {
            nodes.retain(|&id| id != node_id);
            
            // Remove empty groups
            if nodes.is_empty() {
                self.groups.remove(group);
            }
        }
    }
    
    /// Remove a node from all groups
    pub fn remove_from_all_groups(&mut self, node_id: NodeId) {
        let groups_to_clean: Vec<String> = self.groups.keys().cloned().collect();
        for group in groups_to_clean {
            self.remove_from_group(node_id, &group);
        }
    }
    
    /// Get all nodes in a group
    pub fn get_nodes_in_group(&self, group: &str) -> Vec<NodeId> {
        self.groups.get(group).cloned().unwrap_or_default()
    }
    
    /// Check if a node is in a group
    pub fn is_in_group(&self, node_id: NodeId, group: &str) -> bool {
        self.groups.get(group).map_or(false, |nodes| nodes.contains(&node_id))
    }
    
    /// Get all groups a node belongs to
    pub fn get_groups_for_node(&self, node_id: NodeId) -> Vec<String> {
        self.groups
            .iter()
            .filter_map(|(group, nodes)| {
                if nodes.contains(&node_id) {
                    Some(group.clone())
                } else {
                    None
                }
            })
            .collect()
    }
    
    /// Get all group names
    pub fn get_all_groups(&self) -> Vec<String> {
        self.groups.keys().cloned().collect()
    }
}
