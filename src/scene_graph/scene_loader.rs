use super::*;
use std::collections::HashMap;
use std::fs;
use std::path::Path;
use serde::{Deserialize, Serialize};

/// Scene serialization format
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct SerializedScene {
    pub name: String,
    pub version: String,
    pub nodes: Vec<SerializedNode>,
    pub connections: Vec<SerializedConnection>,
    pub metadata: HashMap<String, String>,
}

/// Serialized node data
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct SerializedNode {
    pub id: String,
    pub name: String,
    pub node_type: String,
    pub parent_id: Option<String>,
    pub transform: SerializedTransform,
    pub properties: HashMap<String, SerializedProperty>,
    pub groups: Vec<String>,
    pub visible: bool,
    pub process_mode: String,
}

/// Serialized transform data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SerializedTransform {
    pub position: [f32; 2],
    pub rotation: f32,
    pub scale: [f32; 2],
    pub z_index: i32,
}

/// Serialized property value
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
#[serde(tag = "type", content = "value")]
pub enum SerializedProperty {
    Bool(bool),
    Int(i32),
    Float(f32),
    String(String),
    Vector2([f32; 2]),
    Vector3([f32; 3]),
    Color([f32; 4]),
    Resource(String), // Path to resource
    Array(Vec<SerializedProperty>),
    Dictionary(HashMap<String, SerializedProperty>),
}

/// Serialized signal connection
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SerializedConnection {
    pub source_node: String,
    pub signal_name: String,
    pub target_node: String,
    pub method_name: String,
    pub flags: u32,
}

/// Scene loader for loading and saving scenes
#[derive(Debug)]
pub struct SceneLoader {
    /// Resource system for loading dependencies
    resource_system: Option<crate::resources::ResourceSystem>,
}

impl SceneLoader {
    /// Create a new scene loader
    pub fn new() -> Self {
        Self {
            resource_system: None,
        }
    }
    
    /// Set the resource system for loading dependencies
    pub fn set_resource_system(&mut self, resource_system: crate::resources::ResourceSystem) {
        self.resource_system = Some(resource_system);
    }
    
    /// Load a scene from a file
    pub fn load_scene(&mut self, path: &str) -> Result<Scene, SceneLoadError> {
        let content = fs::read_to_string(path)
            .map_err(|e| SceneLoadError::IoError(e.to_string()))?;
        
        let serialized: SerializedScene = if path.ends_with(".json") {
            serde_json::from_str(&content)
                .map_err(|e| SceneLoadError::ParseError(e.to_string()))?
        } else {
            return Err(SceneLoadError::UnsupportedFormat(path.to_string()));
        };
        
        self.deserialize_scene(serialized)
    }
    
    /// Save a scene to a file
    pub fn save_scene(&self, scene: &Scene, path: &str) -> Result<(), SceneLoadError> {
        let serialized = self.serialize_scene(scene)?;
        
        let content = if path.ends_with(".json") {
            serde_json::to_string_pretty(&serialized)
                .map_err(|e| SceneLoadError::SerializationError(e.to_string()))?
        } else {
            return Err(SceneLoadError::UnsupportedFormat(path.to_string()));
        };
        
        fs::write(path, content)
            .map_err(|e| SceneLoadError::IoError(e.to_string()))?;
        
        Ok(())
    }
    
    /// Serialize a scene to the serialized format
    pub fn serialize_scene(&self, scene: &Scene) -> Result<SerializedScene, SceneLoadError> {
        let mut serialized_nodes = Vec::new();
        let mut serialized_connections = Vec::new();
        
        // Serialize all nodes
        for (node_id, node) in scene.get_nodes() {
            let serialized_node = self.serialize_node(node_id, node)?;
            serialized_nodes.push(serialized_node);
        }
        
        // TODO: Serialize signal connections
        // This would require access to the signal system
        
        Ok(SerializedScene {
            name: scene.name().to_string(),
            version: "1.0".to_string(),
            nodes: serialized_nodes,
            connections: serialized_connections,
            metadata: HashMap::new(),
        })
    }
    
    /// Serialize a single node
    fn serialize_node(&self, node_id: &NodeId, node: &Box<dyn Node>) -> Result<SerializedNode, SceneLoadError> {
        let properties = HashMap::new();

        // For simplicity, just serialize as a basic node
        let node_type = "Node";

        Ok(SerializedNode {
            id: format!("{:?}", node_id),
            name: node.name().to_string(),
            node_type: node_type.to_string(),
            parent_id: node.parent().map(|p| format!("{:?}", p)),
            transform: SerializedTransform {
                position: [0.0, 0.0],
                rotation: 0.0,
                scale: [1.0, 1.0],
                z_index: 0,
            },
            properties,
            groups: node.get_groups().to_vec(),
            visible: true,
            process_mode: "Inherit".to_string(),
        })
    }
    
    /// Deserialize a scene from the serialized format
    pub fn deserialize_scene(&mut self, serialized: SerializedScene) -> Result<Scene, SceneLoadError> {
        let mut scene = Scene::new(serialized.name);
        let mut node_id_map = HashMap::new();
        
        // First pass: create all nodes
        for serialized_node in &serialized.nodes {
            let node = self.deserialize_node(serialized_node)?;
            let node_id = scene.add_node(node);
            node_id_map.insert(serialized_node.id.clone(), node_id);
        }
        
        // Second pass: set up parent-child relationships
        for serialized_node in &serialized.nodes {
            if let Some(parent_id_str) = &serialized_node.parent_id {
                if let (Some(&child_id), Some(&parent_id)) = (
                    node_id_map.get(&serialized_node.id),
                    node_id_map.get(parent_id_str)
                ) {
                    scene.set_parent(child_id, Some(parent_id));
                }
            }
        }
        
        // TODO: Restore signal connections
        
        Ok(scene)
    }
    
    /// Deserialize a single node
    fn deserialize_node(&self, serialized: &SerializedNode) -> Result<Box<dyn Node>, SceneLoadError> {
        // For simplicity, just create a basic node
        let node: Box<dyn Node> = Box::new(BaseNode::new(serialized.name.clone()));
        
        Ok(node)
    }
}

impl Default for SceneLoader {
    fn default() -> Self {
        Self::new()
    }
}

/// Scene loading errors
#[derive(Debug, Clone)]
pub enum SceneLoadError {
    IoError(String),
    ParseError(String),
    SerializationError(String),
    UnsupportedFormat(String),
    UnknownNodeType(String),
    MissingDependency(String),
    InvalidScene(String),
}

impl std::fmt::Display for SceneLoadError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            SceneLoadError::IoError(msg) => write!(f, "IO error: {}", msg),
            SceneLoadError::ParseError(msg) => write!(f, "Parse error: {}", msg),
            SceneLoadError::SerializationError(msg) => write!(f, "Serialization error: {}", msg),
            SceneLoadError::UnsupportedFormat(format) => write!(f, "Unsupported format: {}", format),
            SceneLoadError::UnknownNodeType(node_type) => write!(f, "Unknown node type: {}", node_type),
            SceneLoadError::MissingDependency(dep) => write!(f, "Missing dependency: {}", dep),
            SceneLoadError::InvalidScene(msg) => write!(f, "Invalid scene: {}", msg),
        }
    }
}

impl std::error::Error for SceneLoadError {}
