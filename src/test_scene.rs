use crate::scene_graph::*;
use nalgebra::Vector2;

/// Create a comprehensive test scene demonstrating Godot-style features
pub fn create_test_scene() -> Scene {
    let mut scene = Scene::new("Comprehensive Test Scene".to_string());
    
    // Create root node (like <PERSON><PERSON>'s Main scene)
    let root_node = BaseNode2D::new("Root".to_string());
    let root_id = scene.add_node(Box::new(root_node));
    
    // Create a player sprite (Sprite2D)
    let mut player_sprite = Sprite2D::triangle_with_position(
        "Player".to_string(),
        Vector2::new(-0.5, 0.0),
        0.15,
        [0.2, 0.8, 0.2, 1.0], // Green
    );
    player_sprite.set_z_index(10); // Higher z-index for player
    let player_id = scene.add_node(Box::new(player_sprite));
    
    // Add player to "players" group
    scene.add_node_to_group(player_id, "players".to_string());
    
    // Create enemy sprites
    for i in 0..3 {
        let mut enemy_sprite = Sprite2D::triangle_with_position(
            format!("Enemy_{}", i),
            Vector2::new(0.3 + i as f32 * 0.2, -0.3 + i as f32 * 0.15),
            0.1,
            [0.8, 0.2, 0.2, 1.0], // Red
        );
        enemy_sprite.set_z_index(5); // Lower z-index than player
        let enemy_id = scene.add_node(Box::new(enemy_sprite));
        
        // Add enemies to "enemies" group
        scene.add_node_to_group(enemy_id, "enemies".to_string());
    }
    
    // Create background elements
    let mut background_sprite = Sprite2D::triangle_with_position(
        "Background".to_string(),
        Vector2::new(0.0, 0.5),
        0.3,
        [0.3, 0.3, 0.8, 0.5], // Blue with transparency
    );
    background_sprite.set_z_index(-10); // Behind everything
    let background_id = scene.add_node(Box::new(background_sprite));
    
    // Add background to "background" group
    scene.add_node_to_group(background_id, "background".to_string());
    
    // Create UI elements (Control nodes)
    let mut ui_root = Control::new("UI".to_string());
    ui_root.set_size(Vector2::new(800.0, 600.0));
    let ui_root_id = scene.add_node(Box::new(ui_root));
    
    // Create a label
    let mut score_label = Label::with_text("ScoreLabel".to_string(), "Score: 0".to_string());
    score_label.set_position(Vector2::new(10.0, 10.0));
    score_label.set_size(Vector2::new(200.0, 30.0));
    let score_label_id = scene.add_node(Box::new(score_label));
    
    // Add label to "ui" group
    scene.add_node_to_group(score_label_id, "ui".to_string());
    
    // Create a button
    let mut start_button = Button::with_text("StartButton".to_string(), "Start Game".to_string());
    start_button.set_position(Vector2::new(350.0, 500.0));
    start_button.set_size(Vector2::new(100.0, 40.0));
    let start_button_id = scene.add_node(Box::new(start_button));
    
    // Add button to "ui" group
    scene.add_node_to_group(start_button_id, "ui".to_string());
    
    // Create physics bodies (RigidBody2D)
    let mut physics_body = RigidBody2D::new("PhysicsBox".to_string());
    physics_body.set_mass(2.0);
    physics_body.set_linear_velocity(Vector2::new(0.1, 0.0));
    physics_body.set_position(Vector2::new(0.0, -0.5));
    let physics_body_id = scene.add_node(Box::new(physics_body));
    
    // Add physics body to "physics" group
    scene.add_node_to_group(physics_body_id, "physics".to_string());
    
    // Create collision shape for physics body
    let mut collision_shape = CollisionShape2D::rectangle("CollisionShape".to_string(), 0.1, 0.1);
    collision_shape.set_position(Vector2::new(0.0, -0.5));
    let collision_shape_id = scene.add_node(Box::new(collision_shape));
    
    // Create an Area2D for detection
    let mut detection_area = Area2D::new("DetectionArea".to_string());
    detection_area.set_monitoring(true);
    detection_area.set_position(Vector2::new(0.5, 0.0));
    let detection_area_id = scene.add_node(Box::new(detection_area));
    
    // Add area to "areas" group
    scene.add_node_to_group(detection_area_id, "areas".to_string());
    
    // Create a CharacterBody2D for player movement
    let mut character_body = CharacterBody2D::new("PlayerCharacter".to_string());
    character_body.set_velocity(Vector2::new(0.0, 0.0));
    character_body.set_position(Vector2::new(-0.8, 0.0));
    let character_body_id = scene.add_node(Box::new(character_body));
    
    // Add character to "characters" group
    scene.add_node_to_group(character_body_id, "characters".to_string());
    
    // Set up some basic hierarchy (parent-child relationships)
    // In a real implementation, we'd properly set up the node tree
    
    // Create signals for the scene
    let mut signal_system = scene.signal_system_mut();
    let player_hit_signal = signal_system.create_signal("player_hit".to_string());
    let enemy_destroyed_signal = signal_system.create_signal("enemy_destroyed".to_string());
    let game_over_signal = signal_system.create_signal("game_over".to_string());
    
    // Connect some signals (in a real implementation, these would be connected to actual methods)
    scene.connect_signal(
        player_hit_signal.clone(),
        score_label_id,
        "update_score".to_string(),
        ConnectFlags::NONE,
    ).ok();
    
    scene.connect_signal(
        enemy_destroyed_signal.clone(),
        score_label_id,
        "add_points".to_string(),
        ConnectFlags::NONE,
    ).ok();
    
    scene.connect_signal(
        game_over_signal.clone(),
        start_button_id,
        "show_restart".to_string(),
        ConnectFlags::NONE,
    ).ok();
    
    println!("Created comprehensive test scene with:");
    println!("  - Groups: players ({}), enemies ({}), background ({}), ui ({}), physics ({}), areas ({}), characters ({})",
        scene.get_nodes_in_group("players").len(),
        scene.get_nodes_in_group("enemies").len(),
        scene.get_nodes_in_group("background").len(),
        scene.get_nodes_in_group("ui").len(),
        scene.get_nodes_in_group("physics").len(),
        scene.get_nodes_in_group("areas").len(),
        scene.get_nodes_in_group("characters").len(),
    );
    println!("  - Signals: player_hit, enemy_destroyed, game_over");
    println!("  - Node types: Sprite2D, Control, Label, Button, RigidBody2D, CollisionShape2D, Area2D, CharacterBody2D");
    
    scene
}

/// Demonstrate scene operations
pub fn demonstrate_scene_operations(scene: &mut Scene) {
    println!("\n=== Demonstrating Scene Operations ===");
    
    // Test group operations
    println!("Players in scene: {:?}", scene.get_nodes_in_group("players"));
    println!("Enemies in scene: {:?}", scene.get_nodes_in_group("enemies"));
    
    // Test signal emission (placeholder)
    // Create a test signal for demonstration
    let test_signal = scene.signal_system_mut().create_signal("test_signal".to_string());
    println!("Emitting test signal: {}", test_signal.name);
    scene.emit_signal(test_signal, vec![]).ok();
    
    // Test calling group methods
    scene.call_group("enemies", "take_damage");
    scene.call_group("ui", "update_display");
    
    // Test node path finding
    if let Some(node_id) = scene.get_node_by_path("/Player") {
        println!("Found player node at path: {:?}", node_id);
    }
    
    println!("Scene operations demonstration complete.");
}

/// Create a simple test scene for basic functionality
pub fn create_simple_test_scene() -> Scene {
    let mut scene = Scene::new("Simple Test Scene".to_string());
    
    // Create a simple triangle sprite
    let triangle_sprite = Sprite2D::triangle_with_position(
        "Triangle".to_string(),
        Vector2::new(-0.8, 0.8), // Top-left position
        0.2,
        [0.3, 0.6, 1.0, 1.0], // Blue color
    );
    
    let triangle_id = scene.add_node(Box::new(triangle_sprite));
    scene.add_node_to_group(triangle_id, "test_objects".to_string());
    
    println!("Created simple test scene with triangle at top-left");
    
    scene
}

/// Test lifecycle methods
pub fn test_lifecycle_methods(scene: &mut Scene) {
    println!("\n=== Testing Lifecycle Methods ===");
    
    // Process the scene (calls _process on all nodes)
    scene.process_nodes(0.016); // 60 FPS delta
    
    // Physics process the scene
    scene.physics_process_nodes(0.016);
    
    println!("Lifecycle methods test complete.");
}

/// Test the SceneTree functionality
pub fn test_scene_tree() {
    println!("\n=== Testing SceneTree ===");
    
    let mut scene_tree = SceneTree::new();
    let test_scene = create_simple_test_scene();
    
    scene_tree.change_scene_to(test_scene);
    
    // Test pause/unpause
    scene_tree.set_pause(true);
    println!("Scene tree paused: {}", scene_tree.is_paused());
    
    scene_tree.set_pause(false);
    println!("Scene tree unpaused: {}", scene_tree.is_paused());
    
    // Test processing
    scene_tree.process(0.016);
    scene_tree.physics_process(0.016);
    
    // Test group operations
    let test_nodes = scene_tree.get_nodes_in_group("test_objects");
    println!("Test objects in scene tree: {:?}", test_nodes);
    
    println!("SceneTree test complete.");
}
