use std::f32::consts::PI;

/// Easing types for animations - matches <PERSON><PERSON>'s Tween easing
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq)]
pub enum EasingType {
    Linear,
    Sine,
    Quint,
    Quart,
    Quad,
    Expo,
    Elastic,
    Cubic,
    Circ,
    <PERSON><PERSON><PERSON>,
    Back,
}

/// Transition types for animations - matches <PERSON><PERSON>'s Tween transitions
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Copy, PartialEq, Eq)]
pub enum TransitionType {
    Linear,
    EaseIn,
    EaseOut,
    EaseInOut,
    EaseOutIn,
}

impl EasingType {
    /// Apply the easing function to a normalized time value (0.0 to 1.0)
    pub fn apply(self, t: f32) -> f32 {
        let t = t.clamp(0.0, 1.0);
        
        match self {
            EasingType::Linear => t,
            EasingType::Sine => self.apply_sine(t),
            EasingType::Quint => self.apply_quint(t),
            EasingType::Quart => self.apply_quart(t),
            EasingType::Quad => self.apply_quad(t),
            EasingType::Expo => self.apply_expo(t),
            EasingType::Elastic => self.apply_elastic(t),
            EasingType::Cubic => self.apply_cubic(t),
            EasingType::Circ => self.apply_circ(t),
            EasingType::Bounce => self.apply_bounce(t),
            EasingType::Back => self.apply_back(t),
        }
    }
    
    fn apply_sine(self, t: f32) -> f32 {
        1.0 - (t * PI / 2.0).cos()
    }
    
    fn apply_quint(self, t: f32) -> f32 {
        t * t * t * t * t
    }
    
    fn apply_quart(self, t: f32) -> f32 {
        t * t * t * t
    }
    
    fn apply_quad(self, t: f32) -> f32 {
        t * t
    }
    
    fn apply_expo(self, t: f32) -> f32 {
        if t == 0.0 {
            0.0
        } else {
            2.0_f32.powf(10.0 * (t - 1.0))
        }
    }
    
    fn apply_elastic(self, t: f32) -> f32 {
        if t == 0.0 || t == 1.0 {
            t
        } else {
            let p = 0.3;
            let s = p / 4.0;
            -(2.0_f32.powf(10.0 * (t - 1.0)) * ((t - 1.0 - s) * (2.0 * PI) / p).sin())
        }
    }
    
    fn apply_cubic(self, t: f32) -> f32 {
        t * t * t
    }
    
    fn apply_circ(self, t: f32) -> f32 {
        1.0 - (1.0 - t * t).sqrt()
    }
    
    fn apply_bounce(self, t: f32) -> f32 {
        if t < 1.0 / 2.75 {
            7.5625 * t * t
        } else if t < 2.0 / 2.75 {
            let t = t - 1.5 / 2.75;
            7.5625 * t * t + 0.75
        } else if t < 2.5 / 2.75 {
            let t = t - 2.25 / 2.75;
            7.5625 * t * t + 0.9375
        } else {
            let t = t - 2.625 / 2.75;
            7.5625 * t * t + 0.984375
        }
    }
    
    fn apply_back(self, t: f32) -> f32 {
        let s = 1.70158;
        t * t * ((s + 1.0) * t - s)
    }
}

impl TransitionType {
    /// Apply the transition to an eased value
    pub fn apply(self, eased_value: f32, original_t: f32) -> f32 {
        match self {
            TransitionType::Linear => eased_value,
            TransitionType::EaseIn => eased_value,
            TransitionType::EaseOut => 1.0 - (1.0 - original_t).powf(2.0),
            TransitionType::EaseInOut => {
                if original_t < 0.5 {
                    2.0 * original_t * original_t
                } else {
                    1.0 - (-2.0 * original_t + 2.0).powf(2.0) / 2.0
                }
            }
            TransitionType::EaseOutIn => {
                if original_t < 0.5 {
                    let t = original_t * 2.0;
                    (1.0 - (1.0 - t).powf(2.0)) / 2.0
                } else {
                    let t = (original_t - 0.5) * 2.0;
                    (t * t) / 2.0 + 0.5
                }
            }
        }
    }
}

/// Interpolation utilities
pub struct Interpolation;

impl Interpolation {
    /// Linear interpolation between two values
    pub fn lerp(from: f32, to: f32, t: f32) -> f32 {
        from + (to - from) * t
    }
    
    /// Smooth step interpolation (cubic Hermite interpolation)
    pub fn smoothstep(from: f32, to: f32, t: f32) -> f32 {
        let t = t.clamp(0.0, 1.0);
        let t = t * t * (3.0 - 2.0 * t);
        Self::lerp(from, to, t)
    }
    
    /// Smoother step interpolation (quintic Hermite interpolation)
    pub fn smootherstep(from: f32, to: f32, t: f32) -> f32 {
        let t = t.clamp(0.0, 1.0);
        let t = t * t * t * (t * (t * 6.0 - 15.0) + 10.0);
        Self::lerp(from, to, t)
    }
    
    /// Cubic Bezier interpolation
    pub fn cubic_bezier(p0: f32, p1: f32, p2: f32, p3: f32, t: f32) -> f32 {
        let t = t.clamp(0.0, 1.0);
        let u = 1.0 - t;
        let tt = t * t;
        let uu = u * u;
        let uuu = uu * u;
        let ttt = tt * t;
        
        uuu * p0 + 3.0 * uu * t * p1 + 3.0 * u * tt * p2 + ttt * p3
    }
    
    /// Catmull-Rom spline interpolation
    pub fn catmull_rom(p0: f32, p1: f32, p2: f32, p3: f32, t: f32) -> f32 {
        let t = t.clamp(0.0, 1.0);
        let tt = t * t;
        let ttt = tt * t;
        
        0.5 * (
            2.0 * p1 +
            (-p0 + p2) * t +
            (2.0 * p0 - 5.0 * p1 + 4.0 * p2 - p3) * tt +
            (-p0 + 3.0 * p1 - 3.0 * p2 + p3) * ttt
        )
    }
    
    /// Spherical linear interpolation for angles
    pub fn slerp_angle(from: f32, to: f32, t: f32) -> f32 {
        let mut diff = to - from;
        
        // Normalize to [-PI, PI]
        while diff > PI {
            diff -= 2.0 * PI;
        }
        while diff < -PI {
            diff += 2.0 * PI;
        }
        
        from + diff * t
    }
}

/// Easing curve for custom easing functions
#[derive(Debug, Clone)]
pub struct EasingCurve {
    points: Vec<(f32, f32)>, // (time, value) pairs
}

impl EasingCurve {
    /// Create a new easing curve
    pub fn new() -> Self {
        Self {
            points: vec![(0.0, 0.0), (1.0, 1.0)],
        }
    }
    
    /// Add a control point to the curve
    pub fn add_point(&mut self, time: f32, value: f32) {
        self.points.push((time.clamp(0.0, 1.0), value));
        self.points.sort_by(|a, b| a.0.partial_cmp(&b.0).unwrap());
    }
    
    /// Sample the curve at a given time
    pub fn sample(&self, t: f32) -> f32 {
        let t = t.clamp(0.0, 1.0);
        
        if self.points.is_empty() {
            return t;
        }
        
        // Find the two points to interpolate between
        for i in 0..self.points.len() - 1 {
            let (t0, v0) = self.points[i];
            let (t1, v1) = self.points[i + 1];
            
            if t >= t0 && t <= t1 {
                if t1 - t0 == 0.0 {
                    return v0;
                }
                
                let local_t = (t - t0) / (t1 - t0);
                return Interpolation::lerp(v0, v1, local_t);
            }
        }
        
        // If we're outside the range, return the closest value
        if t <= self.points[0].0 {
            self.points[0].1
        } else {
            self.points.last().unwrap().1
        }
    }
}

impl Default for EasingCurve {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_linear_easing() {
        assert_eq!(EasingType::Linear.apply(0.0), 0.0);
        assert_eq!(EasingType::Linear.apply(0.5), 0.5);
        assert_eq!(EasingType::Linear.apply(1.0), 1.0);
    }
    
    #[test]
    fn test_quad_easing() {
        assert_eq!(EasingType::Quad.apply(0.0), 0.0);
        assert_eq!(EasingType::Quad.apply(0.5), 0.25);
        assert_eq!(EasingType::Quad.apply(1.0), 1.0);
    }
    
    #[test]
    fn test_interpolation() {
        assert_eq!(Interpolation::lerp(0.0, 10.0, 0.5), 5.0);
        assert_eq!(Interpolation::lerp(10.0, 20.0, 0.0), 10.0);
        assert_eq!(Interpolation::lerp(10.0, 20.0, 1.0), 20.0);
    }
    
    #[test]
    fn test_easing_curve() {
        let mut curve = EasingCurve::new();
        curve.add_point(0.5, 0.8);
        
        assert_eq!(curve.sample(0.0), 0.0);
        assert_eq!(curve.sample(0.25), 0.4);
        assert_eq!(curve.sample(0.5), 0.8);
        assert_eq!(curve.sample(1.0), 1.0);
    }
}
