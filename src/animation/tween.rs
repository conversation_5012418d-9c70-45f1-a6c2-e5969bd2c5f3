use super::*;
use std::collections::VecDeque;

/// Tween node for property interpolation - matches <PERSON><PERSON>'s Tween
#[derive(Debug)]
pub struct Tween {
    id: TweenId,
    state: AnimationState,
    current_time: f32,
    speed_scale: f32,
    parallel: bool,
    loops: i32,
    current_loop: i32,
    loop_mode: LoopMode,
    tween_steps: VecDeque<TweenStep>,
    current_step: Option<TweenStep>,
    callbacks: Vec<TweenCallback>,
}

impl Tween {
    /// Create a new tween
    pub fn new(id: TweenId) -> Self {
        Self {
            id,
            state: AnimationState::Stopped,
            current_time: 0.0,
            speed_scale: 1.0,
            parallel: false,
            loops: 0,
            current_loop: 0,
            loop_mode: LoopMode::None,
            tween_steps: VecDeque::new(),
            current_step: None,
            callbacks: Vec::new(),
        }
    }
    
    /// Set the number of loops (0 = infinite)
    pub fn set_loops(&mut self, loops: i32) -> &mut Self {
        self.loops = loops;
        self
    }
    
    /// Set the loop mode
    pub fn set_loop_mode(&mut self, mode: LoopMode) -> &mut Self {
        self.loop_mode = mode;
        self
    }
    
    /// Set speed scale
    pub fn set_speed_scale(&mut self, scale: f32) -> &mut Self {
        self.speed_scale = scale;
        self
    }
    
    /// Set parallel mode (multiple tweens can run simultaneously)
    pub fn set_parallel(&mut self, parallel: bool) -> &mut Self {
        self.parallel = parallel;
        self
    }
    
    /// Tween a property from one value to another
    pub fn tween_property(&mut self, target: NodeId, property: &str, from: AnimatableProperty, to: AnimatableProperty, duration: f32) -> &mut Self {
        let step = TweenStep::Property {
            target,
            property: property.to_string(),
            from,
            to,
            duration,
            elapsed: 0.0,
            easing: EasingType::Linear,
            transition: TransitionType::Linear,
        };
        
        self.tween_steps.push_back(step);
        self
    }
    
    /// Tween a method call
    pub fn tween_method(&mut self, target: NodeId, method: &str, from: f32, to: f32, duration: f32) -> &mut Self {
        let step = TweenStep::Method {
            target,
            method: method.to_string(),
            from,
            to,
            duration,
            elapsed: 0.0,
            easing: EasingType::Linear,
            transition: TransitionType::Linear,
        };
        
        self.tween_steps.push_back(step);
        self
    }
    
    /// Add a callback at a specific time
    pub fn tween_callback(&mut self, callback: TweenCallback) -> &mut Self {
        self.callbacks.push(callback);
        self
    }
    
    /// Add an interval (pause)
    pub fn tween_interval(&mut self, duration: f32) -> &mut Self {
        let step = TweenStep::Interval {
            duration,
            elapsed: 0.0,
        };
        
        self.tween_steps.push_back(step);
        self
    }
    
    /// Set easing for the last added step
    pub fn set_ease(&mut self, easing: EasingType) -> &mut Self {
        if let Some(step) = self.tween_steps.back_mut() {
            step.set_easing(easing);
        }
        self
    }
    
    /// Set transition for the last added step
    pub fn set_trans(&mut self, transition: TransitionType) -> &mut Self {
        if let Some(step) = self.tween_steps.back_mut() {
            step.set_transition(transition);
        }
        self
    }
    
    /// Start the tween
    pub fn play(&mut self) {
        self.state = AnimationState::Playing;
        self.current_time = 0.0;
        self.current_loop = 0;
        
        if self.current_step.is_none() && !self.tween_steps.is_empty() {
            self.current_step = self.tween_steps.pop_front();
        }
    }
    
    /// Stop the tween
    pub fn stop(&mut self) {
        self.state = AnimationState::Stopped;
        self.current_time = 0.0;
        self.current_loop = 0;
        self.current_step = None;
    }
    
    /// Pause the tween
    pub fn pause(&mut self) {
        if self.state == AnimationState::Playing {
            self.state = AnimationState::Paused;
        }
    }
    
    /// Resume the tween
    pub fn resume(&mut self) {
        if self.state == AnimationState::Paused {
            self.state = AnimationState::Playing;
        }
    }
    
    /// Check if the tween is finished
    pub fn is_finished(&self) -> bool {
        self.state == AnimationState::Finished
    }
    
    /// Check if the tween is playing
    pub fn is_playing(&self) -> bool {
        self.state == AnimationState::Playing
    }
    
    /// Update the tween
    pub fn update(&mut self, delta_time: f32) {
        if self.state != AnimationState::Playing {
            return;
        }
        
        let scaled_delta = delta_time * self.speed_scale;
        self.current_time += scaled_delta;
        
        // Process current step
        if let Some(ref mut step) = self.current_step {
            step.update(scaled_delta);
            
            if step.is_finished() {
                // Move to next step
                self.current_step = self.tween_steps.pop_front();
                
                // If no more steps, handle looping
                if self.current_step.is_none() {
                    self.handle_loop_completion();
                }
            }
        } else if self.tween_steps.is_empty() {
            // No steps to process, finish
            self.state = AnimationState::Finished;
        }
        
        // Process callbacks
        self.process_callbacks();
    }
    
    /// Handle loop completion
    fn handle_loop_completion(&mut self) {
        self.current_loop += 1;
        
        // Check if we should continue looping
        if self.loops == 0 || self.current_loop < self.loops {
            // Reset for next loop
            match self.loop_mode {
                LoopMode::None => {
                    self.state = AnimationState::Finished;
                }
                LoopMode::Linear => {
                    // Restart from beginning
                    self.restart_steps();
                }
                LoopMode::PingPong => {
                    // Reverse the steps
                    self.reverse_steps();
                }
            }
        } else {
            self.state = AnimationState::Finished;
        }
    }
    
    /// Restart all steps
    fn restart_steps(&mut self) {
        // This would require storing the original steps
        // For now, just finish
        self.state = AnimationState::Finished;
    }
    
    /// Reverse all steps for ping-pong mode
    fn reverse_steps(&mut self) {
        // This would require reversing the step order and values
        // For now, just finish
        self.state = AnimationState::Finished;
    }
    
    /// Process callbacks
    fn process_callbacks(&mut self) {
        // Process callbacks that should trigger at current time
        for callback in &self.callbacks {
            if callback.should_trigger(self.current_time) {
                // In a real implementation, we would call the callback
                println!("Tween callback triggered at time: {}", self.current_time);
            }
        }
    }
    
    /// Get the current progress (0.0 to 1.0)
    pub fn get_progress(&self) -> f32 {
        if let Some(ref step) = self.current_step {
            step.get_progress()
        } else {
            1.0
        }
    }
    
    /// Kill the tween (stop and remove)
    pub fn kill(&mut self) {
        self.stop();
        // In a real implementation, this would remove the tween from the system
    }
}

/// Individual step in a tween sequence
#[derive(Debug, Clone)]
pub enum TweenStep {
    Property {
        target: NodeId,
        property: String,
        from: AnimatableProperty,
        to: AnimatableProperty,
        duration: f32,
        elapsed: f32,
        easing: EasingType,
        transition: TransitionType,
    },
    Method {
        target: NodeId,
        method: String,
        from: f32,
        to: f32,
        duration: f32,
        elapsed: f32,
        easing: EasingType,
        transition: TransitionType,
    },
    Interval {
        duration: f32,
        elapsed: f32,
    },
}

impl TweenStep {
    /// Update the step
    pub fn update(&mut self, delta_time: f32) {
        match self {
            TweenStep::Property { elapsed, .. } |
            TweenStep::Method { elapsed, .. } |
            TweenStep::Interval { elapsed, .. } => {
                *elapsed += delta_time;
            }
        }
    }
    
    /// Check if the step is finished
    pub fn is_finished(&self) -> bool {
        match self {
            TweenStep::Property { duration, elapsed, .. } |
            TweenStep::Method { duration, elapsed, .. } |
            TweenStep::Interval { duration, elapsed } => {
                *elapsed >= *duration
            }
        }
    }
    
    /// Get the progress of the step (0.0 to 1.0)
    pub fn get_progress(&self) -> f32 {
        match self {
            TweenStep::Property { duration, elapsed, .. } |
            TweenStep::Method { duration, elapsed, .. } |
            TweenStep::Interval { duration, elapsed } => {
                if *duration <= 0.0 {
                    1.0
                } else {
                    (*elapsed / *duration).clamp(0.0, 1.0)
                }
            }
        }
    }
    
    /// Set easing type
    pub fn set_easing(&mut self, easing: EasingType) {
        match self {
            TweenStep::Property { easing: e, .. } |
            TweenStep::Method { easing: e, .. } => {
                *e = easing;
            }
            _ => {}
        }
    }
    
    /// Set transition type
    pub fn set_transition(&mut self, transition: TransitionType) {
        match self {
            TweenStep::Property { transition: t, .. } |
            TweenStep::Method { transition: t, .. } => {
                *t = transition;
            }
            _ => {}
        }
    }
    
    /// Get the current interpolated value
    pub fn get_current_value(&self) -> Option<AnimatableProperty> {
        match self {
            TweenStep::Property { from, to, easing, transition, .. } => {
                let progress = self.get_progress();
                let eased_progress = easing.apply(progress);
                let final_progress = transition.apply(eased_progress, progress);
                
                // Interpolate between from and to
                self.interpolate_properties(from, to, final_progress)
            }
            _ => None,
        }
    }
    
    /// Interpolate between two properties
    fn interpolate_properties(&self, from: &AnimatableProperty, to: &AnimatableProperty, t: f32) -> Option<AnimatableProperty> {
        match (from, to) {
            (AnimatableProperty::Position(from_pos), AnimatableProperty::Position(to_pos)) => {
                Some(AnimatableProperty::Position(from_pos.lerp(to_pos, t)))
            }
            (AnimatableProperty::Rotation(from_rot), AnimatableProperty::Rotation(to_rot)) => {
                Some(AnimatableProperty::Rotation(from_rot + (to_rot - from_rot) * t))
            }
            (AnimatableProperty::Scale(from_scale), AnimatableProperty::Scale(to_scale)) => {
                Some(AnimatableProperty::Scale(from_scale.lerp(to_scale, t)))
            }
            (AnimatableProperty::Modulate(from_color), AnimatableProperty::Modulate(to_color)) => {
                let interpolated = [
                    from_color[0] + (to_color[0] - from_color[0]) * t,
                    from_color[1] + (to_color[1] - from_color[1]) * t,
                    from_color[2] + (to_color[2] - from_color[2]) * t,
                    from_color[3] + (to_color[3] - from_color[3]) * t,
                ];
                Some(AnimatableProperty::Modulate(interpolated))
            }
            (AnimatableProperty::Alpha(from_alpha), AnimatableProperty::Alpha(to_alpha)) => {
                Some(AnimatableProperty::Alpha(from_alpha + (to_alpha - from_alpha) * t))
            }
            (AnimatableProperty::Custom(name, from_val), AnimatableProperty::Custom(_, to_val)) => {
                Some(AnimatableProperty::Custom(name.clone(), from_val + (to_val - from_val) * t))
            }
            _ => None,
        }
    }
}

/// Callback for tween events
#[derive(Debug, Clone)]
pub struct TweenCallback {
    pub trigger_time: f32,
    pub callback_type: CallbackType,
    pub triggered: bool,
}

#[derive(Debug, Clone)]
pub enum CallbackType {
    StepFinished,
    TweenFinished,
    LoopFinished,
    Custom(String),
}

impl TweenCallback {
    /// Create a new callback
    pub fn new(trigger_time: f32, callback_type: CallbackType) -> Self {
        Self {
            trigger_time,
            callback_type,
            triggered: false,
        }
    }
    
    /// Check if the callback should trigger
    pub fn should_trigger(&self, current_time: f32) -> bool {
        !self.triggered && current_time >= self.trigger_time
    }
}
