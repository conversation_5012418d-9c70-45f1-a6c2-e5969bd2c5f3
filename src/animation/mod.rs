use std::collections::HashMap;
use nalgebra::Vector2;
use crate::scene_graph::*;

pub mod tween;
pub mod animation_player;
pub mod animation_resource;
pub mod easing;

pub use tween::*;
pub use animation_player::*;
pub use animation_resource::*;
pub use easing::*;

/// Animation system manager
pub struct AnimationSystem {
    tweens: HashMap<TweenId, Tween>,
    animation_players: HashMap<NodeId, AnimationPlayer>,
    tween_counter: u64,
}

impl AnimationSystem {
    /// Create a new animation system
    pub fn new() -> Self {
        Self {
            tweens: HashMap::new(),
            animation_players: HashMap::new(),
            tween_counter: 0,
        }
    }
    
    /// Create a new tween
    pub fn create_tween(&mut self) -> TweenId {
        let id = TweenId(self.tween_counter);
        self.tween_counter += 1;
        
        let tween = Tween::new(id);
        self.tweens.insert(id, tween);
        id
    }
    
    /// Get a tween by ID
    pub fn get_tween(&self, id: TweenId) -> Option<&Tween> {
        self.tweens.get(&id)
    }
    
    /// Get a tween by ID mutably
    pub fn get_tween_mut(&mut self, id: TweenId) -> Option<&mut Tween> {
        self.tweens.get_mut(&id)
    }
    
    /// Remove a tween
    pub fn remove_tween(&mut self, id: TweenId) {
        self.tweens.remove(&id);
    }
    
    /// Add an animation player to a node
    pub fn add_animation_player(&mut self, node_id: NodeId, player: AnimationPlayer) {
        self.animation_players.insert(node_id, player);
    }
    
    /// Get an animation player for a node
    pub fn get_animation_player(&self, node_id: NodeId) -> Option<&AnimationPlayer> {
        self.animation_players.get(&node_id)
    }
    
    /// Get an animation player for a node mutably
    pub fn get_animation_player_mut(&mut self, node_id: NodeId) -> Option<&mut AnimationPlayer> {
        self.animation_players.get_mut(&node_id)
    }
    
    /// Update all animations
    pub fn update(&mut self, delta_time: f32) {
        // Update all tweens
        let mut completed_tweens = Vec::new();
        for (id, tween) in &mut self.tweens {
            tween.update(delta_time);
            if tween.is_finished() {
                completed_tweens.push(*id);
            }
        }
        
        // Remove completed tweens
        for id in completed_tweens {
            self.tweens.remove(&id);
        }
        
        // Update all animation players
        for player in self.animation_players.values_mut() {
            player.update(delta_time);
        }
    }
    
    /// Stop all animations
    pub fn stop_all(&mut self) {
        for tween in self.tweens.values_mut() {
            tween.stop();
        }
        
        for player in self.animation_players.values_mut() {
            player.stop(true);
        }
    }
    
    /// Pause all animations
    pub fn pause_all(&mut self) {
        for tween in self.tweens.values_mut() {
            tween.pause();
        }
        
        for player in self.animation_players.values_mut() {
            player.pause();
        }
    }
    
    /// Resume all animations
    pub fn resume_all(&mut self) {
        for tween in self.tweens.values_mut() {
            tween.resume();
        }
        
        for player in self.animation_players.values_mut() {
            player.resume();
        }
    }
}

impl Default for AnimationSystem {
    fn default() -> Self {
        Self::new()
    }
}

/// Unique identifier for tweens
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash)]
pub struct TweenId(pub u64);

/// Animation state
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum AnimationState {
    Stopped,
    Playing,
    Paused,
    Finished,
}

/// Animation loop mode
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum LoopMode {
    None,
    Linear,
    PingPong,
}

/// Property that can be animated
#[derive(Debug, Clone)]
pub enum AnimatableProperty {
    Position(Vector2<f32>),
    Rotation(f32),
    Scale(Vector2<f32>),
    Modulate([f32; 4]),
    Alpha(f32),
    Custom(String, f32),
}

impl AnimatableProperty {
    /// Get the property as a float value (for single-value properties)
    pub fn as_float(&self) -> Option<f32> {
        match self {
            AnimatableProperty::Rotation(r) => Some(*r),
            AnimatableProperty::Alpha(a) => Some(*a),
            AnimatableProperty::Custom(_, v) => Some(*v),
            _ => None,
        }
    }
    
    /// Get the property as a Vector2 (for 2D properties)
    pub fn as_vector2(&self) -> Option<Vector2<f32>> {
        match self {
            AnimatableProperty::Position(p) => Some(*p),
            AnimatableProperty::Scale(s) => Some(*s),
            _ => None,
        }
    }
    
    /// Get the property as a color array (for color properties)
    pub fn as_color(&self) -> Option<[f32; 4]> {
        match self {
            AnimatableProperty::Modulate(c) => Some(*c),
            _ => None,
        }
    }
}

/// Animation track type
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum TrackType {
    Position,
    Rotation,
    Scale,
    Method,
    Value,
    Audio,
    Animation,
}

/// Keyframe for animations
#[derive(Debug, Clone)]
pub struct Keyframe {
    pub time: f32,
    pub value: AnimatableProperty,
    pub easing: EasingType,
    pub transition: TransitionType,
}

impl Keyframe {
    /// Create a new keyframe
    pub fn new(time: f32, value: AnimatableProperty) -> Self {
        Self {
            time,
            value,
            easing: EasingType::Linear,
            transition: TransitionType::Linear,
        }
    }
    
    /// Set easing type
    pub fn with_easing(mut self, easing: EasingType) -> Self {
        self.easing = easing;
        self
    }
    
    /// Set transition type
    pub fn with_transition(mut self, transition: TransitionType) -> Self {
        self.transition = transition;
        self
    }
}

/// Animation track containing keyframes
#[derive(Debug, Clone)]
pub struct AnimationTrack {
    pub track_type: TrackType,
    pub property_path: String,
    pub keyframes: Vec<Keyframe>,
    pub enabled: bool,
}

impl AnimationTrack {
    /// Create a new animation track
    pub fn new(track_type: TrackType, property_path: String) -> Self {
        Self {
            track_type,
            property_path,
            keyframes: Vec::new(),
            enabled: true,
        }
    }
    
    /// Add a keyframe to the track
    pub fn add_keyframe(&mut self, keyframe: Keyframe) {
        self.keyframes.push(keyframe);
        // Sort keyframes by time
        self.keyframes.sort_by(|a, b| a.time.partial_cmp(&b.time).unwrap());
    }
    
    /// Get value at a specific time
    pub fn get_value_at_time(&self, time: f32) -> Option<AnimatableProperty> {
        if self.keyframes.is_empty() {
            return None;
        }
        
        // Find the keyframes to interpolate between
        let mut prev_keyframe = &self.keyframes[0];
        let mut next_keyframe = &self.keyframes[0];
        
        for keyframe in &self.keyframes {
            if keyframe.time <= time {
                prev_keyframe = keyframe;
            } else {
                next_keyframe = keyframe;
                break;
            }
        }
        
        // If we're at or before the first keyframe
        if time <= prev_keyframe.time {
            return Some(prev_keyframe.value.clone());
        }
        
        // If we're at or after the last keyframe
        if time >= self.keyframes.last().unwrap().time {
            return Some(self.keyframes.last().unwrap().value.clone());
        }
        
        // Interpolate between keyframes
        let duration = next_keyframe.time - prev_keyframe.time;
        if duration <= 0.0 {
            return Some(prev_keyframe.value.clone());
        }
        
        let progress = (time - prev_keyframe.time) / duration;
        let eased_progress = next_keyframe.easing.apply(progress);
        
        self.interpolate_values(&prev_keyframe.value, &next_keyframe.value, eased_progress)
    }
    
    /// Interpolate between two property values
    fn interpolate_values(&self, from: &AnimatableProperty, to: &AnimatableProperty, t: f32) -> Option<AnimatableProperty> {
        match (from, to) {
            (AnimatableProperty::Position(from_pos), AnimatableProperty::Position(to_pos)) => {
                Some(AnimatableProperty::Position(from_pos.lerp(to_pos, t)))
            }
            (AnimatableProperty::Rotation(from_rot), AnimatableProperty::Rotation(to_rot)) => {
                Some(AnimatableProperty::Rotation(from_rot + (to_rot - from_rot) * t))
            }
            (AnimatableProperty::Scale(from_scale), AnimatableProperty::Scale(to_scale)) => {
                Some(AnimatableProperty::Scale(from_scale.lerp(to_scale, t)))
            }
            (AnimatableProperty::Modulate(from_color), AnimatableProperty::Modulate(to_color)) => {
                let interpolated = [
                    from_color[0] + (to_color[0] - from_color[0]) * t,
                    from_color[1] + (to_color[1] - from_color[1]) * t,
                    from_color[2] + (to_color[2] - from_color[2]) * t,
                    from_color[3] + (to_color[3] - from_color[3]) * t,
                ];
                Some(AnimatableProperty::Modulate(interpolated))
            }
            (AnimatableProperty::Alpha(from_alpha), AnimatableProperty::Alpha(to_alpha)) => {
                Some(AnimatableProperty::Alpha(from_alpha + (to_alpha - from_alpha) * t))
            }
            (AnimatableProperty::Custom(name, from_val), AnimatableProperty::Custom(_, to_val)) => {
                Some(AnimatableProperty::Custom(name.clone(), from_val + (to_val - from_val) * t))
            }
            _ => None, // Incompatible types
        }
    }
}
