use super::*;

/// Animation resource - matches <PERSON><PERSON>'s Animation resource
#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)]
pub struct Animation {
    pub name: String,
    pub length: f32,
    pub loop_mode: LoopMode,
    pub step: f32,
    pub tracks: Vec<AnimationTrack>,
}

impl Animation {
    /// Create a new animation
    pub fn new(name: String) -> Self {
        Self {
            name,
            length: 1.0,
            loop_mode: LoopMode::None,
            step: 0.1,
            tracks: Vec::new(),
        }
    }
    
    /// Set the animation length
    pub fn set_length(&mut self, length: f32) {
        self.length = length.max(0.0);
    }
    
    /// Set the loop mode
    pub fn set_loop_mode(&mut self, mode: LoopMode) {
        self.loop_mode = mode;
    }
    
    /// Set the step (for keyframe snapping)
    pub fn set_step(&mut self, step: f32) {
        self.step = step.max(0.001);
    }
    
    /// Add a track
    pub fn add_track(&mut self, track_type: TrackType, at_position: Option<usize>) -> usize {
        let track = AnimationTrack::new(track_type, String::new());
        
        if let Some(pos) = at_position {
            let pos = pos.min(self.tracks.len());
            self.tracks.insert(pos, track);
            pos
        } else {
            self.tracks.push(track);
            self.tracks.len() - 1
        }
    }
    
    /// Remove a track
    pub fn remove_track(&mut self, track_idx: usize) {
        if track_idx < self.tracks.len() {
            self.tracks.remove(track_idx);
        }
    }
    
    /// Get track count
    pub fn get_track_count(&self) -> usize {
        self.tracks.len()
    }
    
    /// Get a track
    pub fn get_track(&self, track_idx: usize) -> Option<&AnimationTrack> {
        self.tracks.get(track_idx)
    }
    
    /// Get a track mutably
    pub fn get_track_mut(&mut self, track_idx: usize) -> Option<&mut AnimationTrack> {
        self.tracks.get_mut(track_idx)
    }
    
    /// Set track path
    pub fn track_set_path(&mut self, track_idx: usize, path: String) {
        if let Some(track) = self.tracks.get_mut(track_idx) {
            track.property_path = path;
        }
    }
    
    /// Get track path
    pub fn track_get_path(&self, track_idx: usize) -> Option<&String> {
        self.tracks.get(track_idx).map(|t| &t.property_path)
    }
    
    /// Set track enabled
    pub fn track_set_enabled(&mut self, track_idx: usize, enabled: bool) {
        if let Some(track) = self.tracks.get_mut(track_idx) {
            track.enabled = enabled;
        }
    }
    
    /// Check if track is enabled
    pub fn track_is_enabled(&self, track_idx: usize) -> bool {
        self.tracks.get(track_idx).map_or(false, |t| t.enabled)
    }
    
    /// Insert a keyframe
    pub fn track_insert_key(&mut self, track_idx: usize, time: f32, key: AnimatableProperty, transition: Option<f32>) {
        if let Some(track) = self.tracks.get_mut(track_idx) {
            let keyframe = Keyframe::new(time, key);
            track.add_keyframe(keyframe);
        }
    }
    
    /// Remove a keyframe
    pub fn track_remove_key(&mut self, track_idx: usize, key_idx: usize) {
        if let Some(track) = self.tracks.get_mut(track_idx) {
            if key_idx < track.keyframes.len() {
                track.keyframes.remove(key_idx);
            }
        }
    }
    
    /// Get keyframe count for a track
    pub fn track_get_key_count(&self, track_idx: usize) -> usize {
        self.tracks.get(track_idx).map_or(0, |t| t.keyframes.len())
    }
    
    /// Get keyframe time
    pub fn track_get_key_time(&self, track_idx: usize, key_idx: usize) -> Option<f32> {
        self.tracks.get(track_idx)
            .and_then(|t| t.keyframes.get(key_idx))
            .map(|k| k.time)
    }
    
    /// Set keyframe time
    pub fn track_set_key_time(&mut self, track_idx: usize, key_idx: usize, time: f32) {
        if let Some(track) = self.tracks.get_mut(track_idx) {
            if let Some(keyframe) = track.keyframes.get_mut(key_idx) {
                keyframe.time = time;
                // Re-sort keyframes
                track.keyframes.sort_by(|a, b| a.time.partial_cmp(&b.time).unwrap());
            }
        }
    }
    
    /// Get keyframe value
    pub fn track_get_key_value(&self, track_idx: usize, key_idx: usize) -> Option<&AnimatableProperty> {
        self.tracks.get(track_idx)
            .and_then(|t| t.keyframes.get(key_idx))
            .map(|k| &k.value)
    }
    
    /// Set keyframe value
    pub fn track_set_key_value(&mut self, track_idx: usize, key_idx: usize, value: AnimatableProperty) {
        if let Some(track) = self.tracks.get_mut(track_idx) {
            if let Some(keyframe) = track.keyframes.get_mut(key_idx) {
                keyframe.value = value;
            }
        }
    }
    
    /// Find a keyframe at a specific time
    pub fn track_find_key(&self, track_idx: usize, time: f32, exact: bool) -> Option<usize> {
        if let Some(track) = self.tracks.get(track_idx) {
            for (i, keyframe) in track.keyframes.iter().enumerate() {
                if exact {
                    if (keyframe.time - time).abs() < 0.001 {
                        return Some(i);
                    }
                } else if keyframe.time >= time {
                    return Some(i);
                }
            }
        }
        None
    }
    
    /// Clear all tracks
    pub fn clear(&mut self) {
        self.tracks.clear();
    }
    
    /// Copy track from another animation
    pub fn copy_track(&mut self, from_animation: &Animation, from_track: usize, to_track: usize) {
        if let Some(source_track) = from_animation.tracks.get(from_track) {
            if to_track < self.tracks.len() {
                self.tracks[to_track] = source_track.clone();
            }
        }
    }
    
    /// Optimize the animation (remove redundant keyframes)
    pub fn optimize(&mut self, allowed_linear_err: f32, allowed_angular_err: f32, max_angle: f32) {
        for i in 0..self.tracks.len() {
            Self::optimize_track(&mut self.tracks[i], allowed_linear_err, allowed_angular_err, max_angle);
        }
    }
    
    /// Optimize a single track
    fn optimize_track(track: &mut AnimationTrack, _allowed_linear_err: f32, _allowed_angular_err: f32, _max_angle: f32) {
        // Simple optimization: remove keyframes that are very close in time
        let mut i = 1;
        while i < track.keyframes.len() - 1 {
            let prev_time = track.keyframes[i - 1].time;
            let curr_time = track.keyframes[i].time;
            let next_time = track.keyframes[i + 1].time;
            
            // If keyframes are very close in time, remove the middle one
            if (curr_time - prev_time) < 0.001 || (next_time - curr_time) < 0.001 {
                track.keyframes.remove(i);
            } else {
                i += 1;
            }
        }
    }
    
    /// Compress the animation
    pub fn compress(&mut self, page_size: u32, fps: f32) {
        // Placeholder for compression algorithm
        println!("Compressing animation '{}' with page_size: {}, fps: {}", self.name, page_size, fps);
    }
}

/// Animation library for managing multiple animations
#[derive(Debug, Default)]
pub struct AnimationLibrary {
    animations: HashMap<String, Animation>,
}

impl AnimationLibrary {
    /// Create a new animation library
    pub fn new() -> Self {
        Self {
            animations: HashMap::new(),
        }
    }
    
    /// Add an animation
    pub fn add_animation(&mut self, name: String, animation: Animation) {
        self.animations.insert(name, animation);
    }
    
    /// Remove an animation
    pub fn remove_animation(&mut self, name: &str) -> Option<Animation> {
        self.animations.remove(name)
    }
    
    /// Get an animation
    pub fn get_animation(&self, name: &str) -> Option<&Animation> {
        self.animations.get(name)
    }
    
    /// Get an animation mutably
    pub fn get_animation_mut(&mut self, name: &str) -> Option<&mut Animation> {
        self.animations.get_mut(name)
    }
    
    /// Get all animation names
    pub fn get_animation_list(&self) -> Vec<String> {
        self.animations.keys().cloned().collect()
    }
    
    /// Check if an animation exists
    pub fn has_animation(&self, name: &str) -> bool {
        self.animations.contains_key(name)
    }
    
    /// Rename an animation
    pub fn rename_animation(&mut self, old_name: &str, new_name: String) -> bool {
        if let Some(animation) = self.animations.remove(old_name) {
            self.animations.insert(new_name, animation);
            true
        } else {
            false
        }
    }
    
    /// Clear all animations
    pub fn clear(&mut self) {
        self.animations.clear();
    }
    
    /// Get the number of animations
    pub fn get_animation_count(&self) -> usize {
        self.animations.len()
    }
}

/// Animation mixer for blending multiple animations
#[derive(Debug)]
pub struct AnimationMixer {
    blend_tree: Vec<AnimationBlendNode>,
    output_tracks: HashMap<String, AnimatableProperty>,
}

impl AnimationMixer {
    /// Create a new animation mixer
    pub fn new() -> Self {
        Self {
            blend_tree: Vec::new(),
            output_tracks: HashMap::new(),
        }
    }
    
    /// Add a blend node
    pub fn add_blend_node(&mut self, node: AnimationBlendNode) {
        self.blend_tree.push(node);
    }
    
    /// Process the blend tree and generate output
    pub fn process(&mut self, delta_time: f32) {
        self.output_tracks.clear();
        
        for node in &mut self.blend_tree {
            node.process(delta_time, &mut self.output_tracks);
        }
    }
    
    /// Get the output value for a track
    pub fn get_output(&self, track_path: &str) -> Option<&AnimatableProperty> {
        self.output_tracks.get(track_path)
    }
}

impl Default for AnimationMixer {
    fn default() -> Self {
        Self::new()
    }
}

/// Animation blend node
#[derive(Debug)]
pub struct AnimationBlendNode {
    pub animation: Animation,
    pub weight: f32,
    pub time_scale: f32,
    pub current_time: f32,
}

impl AnimationBlendNode {
    /// Create a new blend node
    pub fn new(animation: Animation) -> Self {
        Self {
            animation,
            weight: 1.0,
            time_scale: 1.0,
            current_time: 0.0,
        }
    }
    
    /// Process the node
    pub fn process(&mut self, delta_time: f32, output: &mut HashMap<String, AnimatableProperty>) {
        self.current_time += delta_time * self.time_scale;
        
        // Clamp time to animation length
        if self.current_time > self.animation.length {
            match self.animation.loop_mode {
                LoopMode::None => self.current_time = self.animation.length,
                LoopMode::Linear => self.current_time = self.current_time % self.animation.length,
                LoopMode::PingPong => {
                    // TODO: Implement ping-pong
                    self.current_time = self.animation.length;
                }
            }
        }
        
        // Apply tracks with weight
        for track in &self.animation.tracks {
            if let Some(value) = track.get_value_at_time(self.current_time) {
                // Blend with existing output
                if let Some(existing) = output.get(&track.property_path) {
                    // TODO: Implement proper blending
                    output.insert(track.property_path.clone(), value);
                } else {
                    output.insert(track.property_path.clone(), value);
                }
            }
        }
    }
}
