use nalgebra::{Vector2, Isometry2};
use rapier2d::prelude::*;
use std::collections::HashMap;

/// Represents a physics object in the simulation
#[derive(Debug, <PERSON>lone)]
pub struct PhysicsObject {
    pub rigid_body_handle: <PERSON><PERSON>d<PERSON>ody<PERSON><PERSON><PERSON>,
    pub collider_handle: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    pub object_type: ObjectType,
    pub color: [f32; 4],
}

/// Types of physics objects that can be created
#[derive(Debu<PERSON>, <PERSON><PERSON>, Co<PERSON>, PartialEq)]
pub enum ObjectType {
    Ball,
    Box,
    Ground,
}

/// Main physics simulation system
pub struct PhysicsWorld {
    pub rigid_body_set: RigidBodySet,
    pub collider_set: ColliderSet,
    pub gravity: Vector2<f32>,
    pub integration_parameters: IntegrationParameters,
    pub physics_pipeline: PhysicsPipeline,
    pub island_manager: IslandManager,
    pub broad_phase: DefaultBroadPhase,
    pub narrow_phase: NarrowPhase,
    pub impulse_joint_set: ImpulseJointSet,
    pub multibody_joint_set: MultibodyJointSet,
    pub ccd_solver: <PERSON><PERSON><PERSON><PERSON>,
    pub query_pipeline: Query<PERSON><PERSON>eline,
    pub physics_hooks: (),
    pub event_handler: (),
    pub objects: HashMap<RigidBodyHandle, PhysicsObject>,
    pub step_count: u64,
}

impl PhysicsWorld {
    /// Create a new physics world with default settings
    pub fn new() -> Self {
        let mut world = Self {
            rigid_body_set: RigidBodySet::new(),
            collider_set: ColliderSet::new(),
            gravity: Vector2::new(0.0, -9.81),
            integration_parameters: IntegrationParameters::default(),
            physics_pipeline: PhysicsPipeline::new(),
            island_manager: IslandManager::new(),
            broad_phase: DefaultBroadPhase::new(),
            narrow_phase: NarrowPhase::new(),
            impulse_joint_set: ImpulseJointSet::new(),
            multibody_joint_set: MultibodyJointSet::new(),
            ccd_solver: CCDSolver::new(),
            query_pipeline: QueryPipeline::new(),
            physics_hooks: (),
            event_handler: (),
            objects: HashMap::new(),
            step_count: 0,
        };

        // Create ground
        world.create_ground();
        world
    }

    /// Step the physics simulation forward
    pub fn step(&mut self) {
        self.physics_pipeline.step(
            &self.gravity,
            &self.integration_parameters,
            &mut self.island_manager,
            &mut self.broad_phase,
            &mut self.narrow_phase,
            &mut self.rigid_body_set,
            &mut self.collider_set,
            &mut self.impulse_joint_set,
            &mut self.multibody_joint_set,
            &mut self.ccd_solver,
            Some(&mut self.query_pipeline),
            &self.physics_hooks,
            &self.event_handler,
        );
        self.step_count += 1;
    }

    /// Create a ground plane
    fn create_ground(&mut self) {
        let ground_body = RigidBodyBuilder::fixed()
            .translation(Vector2::new(0.0, -5.0))
            .build();
        let ground_handle = self.rigid_body_set.insert(ground_body);

        let ground_collider = ColliderBuilder::cuboid(10.0, 0.5).build();
        let collider_handle = self.collider_set.insert_with_parent(
            ground_collider,
            ground_handle,
            &mut self.rigid_body_set,
        );

        let physics_object = PhysicsObject {
            rigid_body_handle: ground_handle,
            collider_handle,
            object_type: ObjectType::Ground,
            color: [0.5, 0.5, 0.5, 1.0],
        };

        self.objects.insert(ground_handle, physics_object);
    }

    /// Create a dynamic ball
    pub fn create_ball(&mut self, position: Vector2<f32>, radius: f32) -> RigidBodyHandle {
        let ball_body = RigidBodyBuilder::dynamic()
            .translation(position)
            .build();
        let ball_handle = self.rigid_body_set.insert(ball_body);

        let ball_collider = ColliderBuilder::ball(radius)
            .restitution(0.7)
            .friction(0.3)
            .build();
        let collider_handle = self.collider_set.insert_with_parent(
            ball_collider,
            ball_handle,
            &mut self.rigid_body_set,
        );

        let physics_object = PhysicsObject {
            rigid_body_handle: ball_handle,
            collider_handle,
            object_type: ObjectType::Ball,
            color: [1.0, 0.3, 0.3, 1.0],
        };

        self.objects.insert(ball_handle, physics_object);
        ball_handle
    }

    /// Create a dynamic box
    pub fn create_box(&mut self, position: Vector2<f32>, half_extents: Vector2<f32>) -> RigidBodyHandle {
        let box_body = RigidBodyBuilder::dynamic()
            .translation(position)
            .build();
        let box_handle = self.rigid_body_set.insert(box_body);

        let box_collider = ColliderBuilder::cuboid(half_extents.x, half_extents.y)
            .restitution(0.5)
            .friction(0.5)
            .build();
        let collider_handle = self.collider_set.insert_with_parent(
            box_collider,
            box_handle,
            &mut self.rigid_body_set,
        );

        let physics_object = PhysicsObject {
            rigid_body_handle: box_handle,
            collider_handle,
            object_type: ObjectType::Box,
            color: [0.3, 1.0, 0.3, 1.0],
        };

        self.objects.insert(box_handle, physics_object);
        box_handle
    }

    /// Remove an object from the physics world
    pub fn remove_object(&mut self, handle: RigidBodyHandle) {
        if let Some(physics_object) = self.objects.remove(&handle) {
            self.collider_set.remove(
                physics_object.collider_handle,
                &mut self.island_manager,
                &mut self.rigid_body_set,
                true,
            );
            self.rigid_body_set.remove(
                handle,
                &mut self.island_manager,
                &mut self.collider_set,
                &mut self.impulse_joint_set,
                &mut self.multibody_joint_set,
                true,
            );
        }
    }

    /// Get the position and rotation of a rigid body
    pub fn get_body_transform(&self, handle: RigidBodyHandle) -> Option<Isometry2<f32>> {
        self.rigid_body_set.get(handle).map(|body| *body.position())
    }

    /// Apply an impulse to a rigid body
    pub fn apply_impulse(&mut self, handle: RigidBodyHandle, impulse: Vector2<f32>) {
        if let Some(body) = self.rigid_body_set.get_mut(handle) {
            body.apply_impulse(impulse, true);
        }
    }

    /// Set the gravity of the physics world
    pub fn set_gravity(&mut self, gravity: Vector2<f32>) {
        self.gravity = gravity;
    }

    /// Get the number of active rigid bodies
    pub fn active_body_count(&self) -> usize {
        self.rigid_body_set.len()
    }

    /// Get the number of colliders
    pub fn collider_count(&self) -> usize {
        self.collider_set.len()
    }

    /// Clear all dynamic objects (keep ground)
    pub fn clear_dynamic_objects(&mut self) {
        let handles_to_remove: Vec<RigidBodyHandle> = self
            .objects
            .iter()
            .filter(|(_, obj)| obj.object_type != ObjectType::Ground)
            .map(|(handle, _)| *handle)
            .collect();

        for handle in handles_to_remove {
            self.remove_object(handle);
        }
    }
}

impl Default for PhysicsWorld {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_physics_world_creation() {
        let world = PhysicsWorld::new();
        assert_eq!(world.step_count, 0);
        assert!(world.active_body_count() > 0); // Should have ground
    }

    #[test]
    fn test_ball_creation() {
        let mut world = PhysicsWorld::new();
        let initial_count = world.active_body_count();
        
        let _ball_handle = world.create_ball(Vector2::new(0.0, 5.0), 1.0);
        
        assert_eq!(world.active_body_count(), initial_count + 1);
    }

    #[test]
    fn test_box_creation() {
        let mut world = PhysicsWorld::new();
        let initial_count = world.active_body_count();
        
        let _box_handle = world.create_box(Vector2::new(0.0, 5.0), Vector2::new(1.0, 1.0));
        
        assert_eq!(world.active_body_count(), initial_count + 1);
    }

    #[test]
    fn test_physics_step() {
        let mut world = PhysicsWorld::new();
        let initial_step_count = world.step_count;
        
        world.step();
        
        assert_eq!(world.step_count, initial_step_count + 1);
    }

    #[test]
    fn test_object_removal() {
        let mut world = PhysicsWorld::new();
        let ball_handle = world.create_ball(Vector2::new(0.0, 5.0), 1.0);
        let count_with_ball = world.active_body_count();
        
        world.remove_object(ball_handle);
        
        assert_eq!(world.active_body_count(), count_with_ball - 1);
    }
}
