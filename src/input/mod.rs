use std::collections::{HashMap, HashSet};
use winit::event::{ElementState, KeyEvent, MouseButton, WindowEvent};
use winit::keyboard::{KeyCode, PhysicalKey};

pub mod input_map;
pub mod input_event;
pub mod action;

pub use input_map::*;
pub use input_event::*;
pub use action::*;

/// Input system for handling keyboard, mouse, and gamepad input - matches <PERSON><PERSON>'s Input singleton
#[derive(Debug)]
pub struct InputSystem {
    /// Currently pressed keys
    pressed_keys: HashSet<KeyCode>,
    /// Keys that were just pressed this frame
    just_pressed_keys: HashSet<KeyCode>,
    /// Keys that were just released this frame
    just_released_keys: HashSet<KeyCode>,
    
    /// Currently pressed mouse buttons
    pressed_mouse_buttons: HashSet<MouseButton>,
    /// Mouse buttons that were just pressed this frame
    just_pressed_mouse_buttons: HashSet<MouseButton>,
    /// Mouse buttons that were just released this frame
    just_released_mouse_buttons: HashSet<MouseButton>,
    
    /// Current mouse position
    mouse_position: (f32, f32),
    /// Mouse position delta since last frame
    mouse_delta: (f32, f32),
    /// Mouse wheel delta
    mouse_wheel_delta: (f32, f32),
    
    /// Input map for action mapping
    input_map: InputMap,
    
    /// Action states
    action_states: HashMap<String, ActionState>,
    
    /// Input events queue
    input_events: Vec<InputEventData>,
    
    /// Input strength for analog inputs
    action_strengths: HashMap<String, f32>,
}

impl InputSystem {
    /// Create a new input system
    pub fn new() -> Self {
        Self {
            pressed_keys: HashSet::new(),
            just_pressed_keys: HashSet::new(),
            just_released_keys: HashSet::new(),
            pressed_mouse_buttons: HashSet::new(),
            just_pressed_mouse_buttons: HashSet::new(),
            just_released_mouse_buttons: HashSet::new(),
            mouse_position: (0.0, 0.0),
            mouse_delta: (0.0, 0.0),
            mouse_wheel_delta: (0.0, 0.0),
            input_map: InputMap::new(),
            action_states: HashMap::new(),
            input_events: Vec::new(),
            action_strengths: HashMap::new(),
        }
    }
    
    /// Process a window event
    pub fn process_event(&mut self, event: &WindowEvent) {
        match event {
            WindowEvent::KeyboardInput { event, .. } => {
                self.handle_keyboard_input(event);
            }
            WindowEvent::MouseInput { state, button, .. } => {
                self.handle_mouse_input(*state, *button);
            }
            WindowEvent::CursorMoved { position, .. } => {
                let new_pos = (position.x as f32, position.y as f32);
                self.mouse_delta = (new_pos.0 - self.mouse_position.0, new_pos.1 - self.mouse_position.1);
                self.mouse_position = new_pos;
                
                self.input_events.push(InputEventData::MouseMotion {
                    position: new_pos,
                    relative: self.mouse_delta,
                });
            }
            WindowEvent::MouseWheel { delta, .. } => {
                let wheel_delta = match delta {
                    winit::event::MouseScrollDelta::LineDelta(x, y) => (*x, *y),
                    winit::event::MouseScrollDelta::PixelDelta(pos) => (pos.x as f32, pos.y as f32),
                };
                self.mouse_wheel_delta = wheel_delta;
                
                self.input_events.push(InputEventData::MouseWheel {
                    delta: wheel_delta,
                });
            }
            _ => {}
        }
    }
    
    /// Handle keyboard input
    fn handle_keyboard_input(&mut self, event: &KeyEvent) {
        if let PhysicalKey::Code(key_code) = event.physical_key {
            match event.state {
                ElementState::Pressed => {
                    if !self.pressed_keys.contains(&key_code) {
                        self.just_pressed_keys.insert(key_code);
                        self.input_events.push(InputEventData::KeyPressed {
                            key_code,
                            pressed: true,
                            echo: false,
                        });
                    }
                    self.pressed_keys.insert(key_code);
                }
                ElementState::Released => {
                    self.pressed_keys.remove(&key_code);
                    self.just_released_keys.insert(key_code);
                    self.input_events.push(InputEventData::KeyPressed {
                        key_code,
                        pressed: false,
                        echo: false,
                    });
                }
            }
        }
    }
    
    /// Handle mouse input
    fn handle_mouse_input(&mut self, state: ElementState, button: MouseButton) {
        match state {
            ElementState::Pressed => {
                if !self.pressed_mouse_buttons.contains(&button) {
                    self.just_pressed_mouse_buttons.insert(button);
                    self.input_events.push(InputEventData::MouseButton {
                        button,
                        pressed: true,
                        position: self.mouse_position,
                    });
                }
                self.pressed_mouse_buttons.insert(button);
            }
            ElementState::Released => {
                self.pressed_mouse_buttons.remove(&button);
                self.just_released_mouse_buttons.insert(button);
                self.input_events.push(InputEventData::MouseButton {
                    button,
                    pressed: false,
                    position: self.mouse_position,
                });
            }
        }
    }
    
    /// Update the input system (call at the end of each frame)
    pub fn update(&mut self) {
        // Clear just pressed/released states
        self.just_pressed_keys.clear();
        self.just_released_keys.clear();
        self.just_pressed_mouse_buttons.clear();
        self.just_released_mouse_buttons.clear();
        
        // Reset mouse delta
        self.mouse_delta = (0.0, 0.0);
        self.mouse_wheel_delta = (0.0, 0.0);
        
        // Update action states
        self.update_action_states();
        
        // Clear input events
        self.input_events.clear();
    }
    
    /// Update action states based on current input
    fn update_action_states(&mut self) {
        for (action_name, action) in &self.input_map.actions {
            let mut is_pressed = false;
            let mut strength = 0.0f32;
            
            // Check all events for this action
            for event in &action.events {
                match event {
                    InputEventType::Key { key_code, .. } => {
                        if self.pressed_keys.contains(key_code) {
                            is_pressed = true;
                            strength = 1.0;
                        }
                    }
                    InputEventType::MouseButton { button, .. } => {
                        if self.pressed_mouse_buttons.contains(button) {
                            is_pressed = true;
                            strength = 1.0;
                        }
                    }
                    InputEventType::MouseMotion { .. } => {
                        // Mouse motion doesn't have a pressed state
                        strength = (self.mouse_delta.0.abs() + self.mouse_delta.1.abs()) / 100.0;
                    }
                    InputEventType::Joypad { .. } => {
                        // TODO: Implement gamepad support
                    }
                }
            }
            
            // Update action state
            let current_state = self.action_states.get(action_name).copied().unwrap_or(ActionState::Released);
            let new_state = match (current_state, is_pressed) {
                (ActionState::Released, true) => ActionState::JustPressed,
                (ActionState::JustPressed, true) => ActionState::Pressed,
                (ActionState::Pressed, true) => ActionState::Pressed,
                (ActionState::Pressed, false) => ActionState::JustReleased,
                (ActionState::JustReleased, false) => ActionState::Released,
                (ActionState::JustReleased, true) => ActionState::JustPressed,
                (_, false) => ActionState::Released,
            };
            
            self.action_states.insert(action_name.clone(), new_state);
            self.action_strengths.insert(action_name.clone(), strength);
        }
    }
    
    // Godot-style input methods
    
    /// Check if a key is currently pressed
    pub fn is_key_pressed(&self, key_code: KeyCode) -> bool {
        self.pressed_keys.contains(&key_code)
    }
    
    /// Check if a key was just pressed this frame
    pub fn is_key_just_pressed(&self, key_code: KeyCode) -> bool {
        self.just_pressed_keys.contains(&key_code)
    }
    
    /// Check if a key was just released this frame
    pub fn is_key_just_released(&self, key_code: KeyCode) -> bool {
        self.just_released_keys.contains(&key_code)
    }
    
    /// Check if a mouse button is currently pressed
    pub fn is_mouse_button_pressed(&self, button: MouseButton) -> bool {
        self.pressed_mouse_buttons.contains(&button)
    }
    
    /// Check if a mouse button was just pressed this frame
    pub fn is_mouse_button_just_pressed(&self, button: MouseButton) -> bool {
        self.just_pressed_mouse_buttons.contains(&button)
    }
    
    /// Check if a mouse button was just released this frame
    pub fn is_mouse_button_just_released(&self, button: MouseButton) -> bool {
        self.just_released_mouse_buttons.contains(&button)
    }
    
    /// Get the current mouse position
    pub fn get_mouse_position(&self) -> (f32, f32) {
        self.mouse_position
    }
    
    /// Get the mouse movement delta
    pub fn get_mouse_delta(&self) -> (f32, f32) {
        self.mouse_delta
    }
    
    /// Get the mouse wheel delta
    pub fn get_mouse_wheel_delta(&self) -> (f32, f32) {
        self.mouse_wheel_delta
    }
    
    // Action-based input methods (Godot-style)
    
    /// Check if an action is currently pressed
    pub fn is_action_pressed(&self, action: &str) -> bool {
        matches!(
            self.action_states.get(action),
            Some(ActionState::Pressed) | Some(ActionState::JustPressed)
        )
    }
    
    /// Check if an action was just pressed this frame
    pub fn is_action_just_pressed(&self, action: &str) -> bool {
        matches!(self.action_states.get(action), Some(ActionState::JustPressed))
    }
    
    /// Check if an action was just released this frame
    pub fn is_action_just_released(&self, action: &str) -> bool {
        matches!(self.action_states.get(action), Some(ActionState::JustReleased))
    }
    
    /// Get the strength of an action (0.0 to 1.0)
    pub fn get_action_strength(&self, action: &str) -> f32 {
        self.action_strengths.get(action).copied().unwrap_or(0.0)
    }
    
    /// Get the input map
    pub fn get_input_map(&self) -> &InputMap {
        &self.input_map
    }
    
    /// Get the input map mutably
    pub fn get_input_map_mut(&mut self) -> &mut InputMap {
        &mut self.input_map
    }
    
    /// Get all input events from this frame
    pub fn get_input_events(&self) -> &[InputEventData] {
        &self.input_events
    }
    
    /// Add an action to the input map
    pub fn add_action(&mut self, name: String, deadzone: f32) {
        self.input_map.add_action(name, deadzone);
    }
    
    /// Remove an action from the input map
    pub fn remove_action(&mut self, name: &str) {
        self.input_map.remove_action(name);
    }
    
    /// Add an input event to an action
    pub fn action_add_event(&mut self, action: &str, event: InputEventType) {
        self.input_map.action_add_event(action, event);
    }
    
    /// Remove an input event from an action
    pub fn action_remove_event(&mut self, action: &str, event: &InputEventType) {
        self.input_map.action_remove_event(action, event);
    }
}

impl Default for InputSystem {
    fn default() -> Self {
        Self::new()
    }
}
