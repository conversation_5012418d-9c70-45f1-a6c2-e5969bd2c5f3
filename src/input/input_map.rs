use super::*;
use std::collections::HashMap;

/// Input map for mapping input events to actions - matches <PERSON><PERSON>'s InputMap
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct InputMap {
    pub actions: HashMap<String, Action>,
}

impl InputMap {
    /// Create a new input map
    pub fn new() -> Self {
        Self {
            actions: HashMap::new(),
        }
    }
    
    /// Create a default input map with common actions
    pub fn default_input_map() -> Self {
        let mut input_map = Self::new();
        
        // Movement actions
        input_map.add_action("move_up".to_string(), 0.5);
        input_map.action_add_event("move_up", InputEventType::Key {
            key_code: KeyCode::KeyW,
            pressed: true,
        });
        input_map.action_add_event("move_up", InputEventType::Key {
            key_code: KeyCode::ArrowUp,
            pressed: true,
        });
        
        input_map.add_action("move_down".to_string(), 0.5);
        input_map.action_add_event("move_down", InputEventType::Key {
            key_code: KeyCode::KeyS,
            pressed: true,
        });
        input_map.action_add_event("move_down", InputEventType::Key {
            key_code: KeyCode::ArrowDown,
            pressed: true,
        });
        
        input_map.add_action("move_left".to_string(), 0.5);
        input_map.action_add_event("move_left", InputEventType::Key {
            key_code: KeyCode::KeyA,
            pressed: true,
        });
        input_map.action_add_event("move_left", InputEventType::Key {
            key_code: KeyCode::ArrowLeft,
            pressed: true,
        });
        
        input_map.add_action("move_right".to_string(), 0.5);
        input_map.action_add_event("move_right", InputEventType::Key {
            key_code: KeyCode::KeyD,
            pressed: true,
        });
        input_map.action_add_event("move_right", InputEventType::Key {
            key_code: KeyCode::ArrowRight,
            pressed: true,
        });
        
        // Action buttons
        input_map.add_action("jump".to_string(), 0.5);
        input_map.action_add_event("jump", InputEventType::Key {
            key_code: KeyCode::Space,
            pressed: true,
        });
        
        input_map.add_action("attack".to_string(), 0.5);
        input_map.action_add_event("attack", InputEventType::MouseButton {
            button: MouseButton::Left,
            pressed: true,
        });
        
        input_map.add_action("interact".to_string(), 0.5);
        input_map.action_add_event("interact", InputEventType::Key {
            key_code: KeyCode::KeyE,
            pressed: true,
        });
        
        // UI actions
        input_map.add_action("ui_accept".to_string(), 0.5);
        input_map.action_add_event("ui_accept", InputEventType::Key {
            key_code: KeyCode::Enter,
            pressed: true,
        });
        input_map.action_add_event("ui_accept", InputEventType::Key {
            key_code: KeyCode::Space,
            pressed: true,
        });
        
        input_map.add_action("ui_cancel".to_string(), 0.5);
        input_map.action_add_event("ui_cancel", InputEventType::Key {
            key_code: KeyCode::Escape,
            pressed: true,
        });
        
        input_map.add_action("ui_select".to_string(), 0.5);
        input_map.action_add_event("ui_select", InputEventType::Key {
            key_code: KeyCode::Enter,
            pressed: true,
        });
        
        input_map.add_action("ui_up".to_string(), 0.5);
        input_map.action_add_event("ui_up", InputEventType::Key {
            key_code: KeyCode::ArrowUp,
            pressed: true,
        });
        
        input_map.add_action("ui_down".to_string(), 0.5);
        input_map.action_add_event("ui_down", InputEventType::Key {
            key_code: KeyCode::ArrowDown,
            pressed: true,
        });
        
        input_map.add_action("ui_left".to_string(), 0.5);
        input_map.action_add_event("ui_left", InputEventType::Key {
            key_code: KeyCode::ArrowLeft,
            pressed: true,
        });
        
        input_map.add_action("ui_right".to_string(), 0.5);
        input_map.action_add_event("ui_right", InputEventType::Key {
            key_code: KeyCode::ArrowRight,
            pressed: true,
        });
        
        input_map
    }
    
    /// Add an action
    pub fn add_action(&mut self, name: String, deadzone: f32) {
        self.actions.insert(name, Action::new(deadzone));
    }
    
    /// Remove an action
    pub fn remove_action(&mut self, name: &str) {
        self.actions.remove(name);
    }
    
    /// Check if an action exists
    pub fn has_action(&self, name: &str) -> bool {
        self.actions.contains_key(name)
    }
    
    /// Get all action names
    pub fn get_actions(&self) -> Vec<String> {
        self.actions.keys().cloned().collect()
    }
    
    /// Add an input event to an action
    pub fn action_add_event(&mut self, action: &str, event: InputEventType) {
        if let Some(action_data) = self.actions.get_mut(action) {
            action_data.add_event(event);
        }
    }
    
    /// Remove an input event from an action
    pub fn action_remove_event(&mut self, action: &str, event: &InputEventType) {
        if let Some(action_data) = self.actions.get_mut(action) {
            action_data.remove_event(event);
        }
    }
    
    /// Get all events for an action
    pub fn action_get_events(&self, action: &str) -> Option<&Vec<InputEventType>> {
        self.actions.get(action).map(|a| &a.events)
    }
    
    /// Set the deadzone for an action
    pub fn action_set_deadzone(&mut self, action: &str, deadzone: f32) {
        if let Some(action_data) = self.actions.get_mut(action) {
            action_data.deadzone = deadzone;
        }
    }
    
    /// Get the deadzone for an action
    pub fn action_get_deadzone(&self, action: &str) -> Option<f32> {
        self.actions.get(action).map(|a| a.deadzone)
    }
    
    /// Clear all events for an action
    pub fn action_erase_events(&mut self, action: &str) {
        if let Some(action_data) = self.actions.get_mut(action) {
            action_data.events.clear();
        }
    }
    
    /// Clear all actions
    pub fn clear(&mut self) {
        self.actions.clear();
    }
    
    /// Load input map from a configuration
    pub fn load_from_config(&mut self, config: InputMapConfig) {
        self.actions = config.actions;
    }
    
    /// Save input map to a configuration
    pub fn save_to_config(&self) -> InputMapConfig {
        InputMapConfig {
            actions: self.actions.clone(),
        }
    }
    
    /// Find actions that use a specific input event
    pub fn find_actions_for_event(&self, event: &InputEventType) -> Vec<String> {
        self.actions
            .iter()
            .filter_map(|(name, action)| {
                if action.events.contains(event) {
                    Some(name.clone())
                } else {
                    None
                }
            })
            .collect()
    }
    
    /// Check if an event conflicts with existing actions
    pub fn event_conflicts(&self, action: &str, event: &InputEventType) -> Vec<String> {
        let mut conflicts = Vec::new();
        
        for (name, action_data) in &self.actions {
            if name != action && action_data.events.contains(event) {
                conflicts.push(name.clone());
            }
        }
        
        conflicts
    }
    
    /// Get a human-readable string for an input event
    pub fn get_event_display_string(&self, event: &InputEventType) -> String {
        match event {
            InputEventType::Key { key_code, .. } => {
                format!("{:?}", key_code)
            }
            InputEventType::MouseButton { button, .. } => {
                match button {
                    MouseButton::Left => "Left Mouse".to_string(),
                    MouseButton::Right => "Right Mouse".to_string(),
                    MouseButton::Middle => "Middle Mouse".to_string(),
                    MouseButton::Back => "Mouse Back".to_string(),
                    MouseButton::Forward => "Mouse Forward".to_string(),
                    MouseButton::Other(n) => format!("Mouse {}", n),
                }
            }
            InputEventType::MouseMotion { .. } => "Mouse Motion".to_string(),
            InputEventType::Joypad { device_id, button, .. } => {
                format!("Gamepad {} Button {}", device_id, button)
            }
        }
    }
    
    /// Get a human-readable string for an action
    pub fn get_action_display_string(&self, action: &str) -> String {
        if let Some(action_data) = self.actions.get(action) {
            if action_data.events.is_empty() {
                "Unassigned".to_string()
            } else {
                action_data.events
                    .iter()
                    .map(|e| self.get_event_display_string(e))
                    .collect::<Vec<_>>()
                    .join(", ")
            }
        } else {
            "Unknown Action".to_string()
        }
    }
}

impl Default for InputMap {
    fn default() -> Self {
        Self::default_input_map()
    }
}

/// Configuration for saving/loading input maps
#[derive(Debug, Clone)]
pub struct InputMapConfig {
    pub actions: HashMap<String, Action>,
}

impl InputMapConfig {
    /// Create a new input map config
    pub fn new() -> Self {
        Self {
            actions: HashMap::new(),
        }
    }
    
    /// Load from JSON string
    pub fn from_json(_json: &str) -> Result<Self, String> {
        // In a real implementation, this would parse JSON
        // For now, return a default config
        Ok(Self::new())
    }
    
    /// Save to JSON string
    pub fn to_json(&self) -> Result<String, String> {
        // In a real implementation, this would serialize to JSON
        // For now, return a placeholder
        Ok("{}".to_string())
    }
}

impl Default for InputMapConfig {
    fn default() -> Self {
        Self::new()
    }
}
