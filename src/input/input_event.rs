use super::*;
use winit::event::<PERSON><PERSON><PERSON><PERSON>;
use winit::keyboard::KeyCode;

/// Input event types - matches <PERSON><PERSON>'s InputEvent hierarchy
#[derive(Debug, <PERSON><PERSON>, PartialEq)]
pub enum InputEventType {
    /// Keyboard input event
    Key {
        key_code: KeyC<PERSON>,
        pressed: bool,
    },
    /// Mouse button input event
    MouseButton {
        button: <PERSON><PERSON><PERSON><PERSON>,
        pressed: bool,
    },
    /// Mouse motion event
    MouseMotion {
        relative: (f32, f32),
    },
    /// Gamepad/Joypad input event
    Joypad {
        device_id: u32,
        button: u32,
        pressed: bool,
    },
}

/// Input event data for the current frame
#[derive(Debug, Clone)]
pub enum InputEventData {
    /// Key was pressed or released
    KeyPressed {
        key_code: KeyCode,
        pressed: bool,
        echo: bool,
    },
    /// Mouse button was pressed or released
    MouseButton {
        button: MouseButton,
        pressed: bool,
        position: (f32, f32),
    },
    /// Mouse was moved
    MouseMotion {
        position: (f32, f32),
        relative: (f32, f32),
    },
    /// Mouse wheel was scrolled
    MouseWheel {
        delta: (f32, f32),
    },
    /// Gamepad button was pressed or released
    JoypadButton {
        device_id: u32,
        button: u32,
        pressed: bool,
    },
    /// Gamepad axis was moved
    JoypadAxis {
        device_id: u32,
        axis: u32,
        value: f32,
    },
}

impl InputEventData {
    /// Check if this event matches an input event type
    pub fn matches(&self, event_type: &InputEventType) -> bool {
        match (self, event_type) {
            (
                InputEventData::KeyPressed { key_code, pressed, .. },
                InputEventType::Key { key_code: target_key, pressed: target_pressed }
            ) => key_code == target_key && pressed == target_pressed,
            
            (
                InputEventData::MouseButton { button, pressed, .. },
                InputEventType::MouseButton { button: target_button, pressed: target_pressed }
            ) => button == target_button && pressed == target_pressed,
            
            (
                InputEventData::MouseMotion { relative, .. },
                InputEventType::MouseMotion { relative: target_relative }
            ) => {
                // For mouse motion, we check if the movement is significant
                let magnitude = (relative.0.abs() + relative.1.abs()).sqrt();
                let target_magnitude = (target_relative.0.abs() + target_relative.1.abs()).sqrt();
                (magnitude - target_magnitude).abs() < 0.1
            }
            
            (
                InputEventData::JoypadButton { device_id, button, pressed },
                InputEventType::Joypad { device_id: target_device, button: target_button, pressed: target_pressed }
            ) => device_id == target_device && button == target_button && pressed == target_pressed,
            
            _ => false,
        }
    }
    
    /// Get the strength/intensity of this input event (0.0 to 1.0)
    pub fn get_strength(&self) -> f32 {
        match self {
            InputEventData::KeyPressed { pressed, .. } => if *pressed { 1.0 } else { 0.0 },
            InputEventData::MouseButton { pressed, .. } => if *pressed { 1.0 } else { 0.0 },
            InputEventData::MouseMotion { relative, .. } => {
                let magnitude = (relative.0 * relative.0 + relative.1 * relative.1).sqrt();
                (magnitude / 100.0).clamp(0.0, 1.0) // Normalize mouse movement
            }
            InputEventData::MouseWheel { delta } => {
                let magnitude = (delta.0 * delta.0 + delta.1 * delta.1).sqrt();
                (magnitude / 10.0).clamp(0.0, 1.0) // Normalize wheel movement
            }
            InputEventData::JoypadButton { pressed, .. } => if *pressed { 1.0 } else { 0.0 },
            InputEventData::JoypadAxis { value, .. } => value.abs().clamp(0.0, 1.0),
        }
    }
    
    /// Check if this is a pressed event
    pub fn is_pressed(&self) -> bool {
        match self {
            InputEventData::KeyPressed { pressed, .. } => *pressed,
            InputEventData::MouseButton { pressed, .. } => *pressed,
            InputEventData::JoypadButton { pressed, .. } => *pressed,
            InputEventData::MouseMotion { .. } => false,
            InputEventData::MouseWheel { .. } => false,
            InputEventData::JoypadAxis { value, .. } => value.abs() > 0.1,
        }
    }
    
    /// Check if this is a released event
    pub fn is_released(&self) -> bool {
        match self {
            InputEventData::KeyPressed { pressed, .. } => !*pressed,
            InputEventData::MouseButton { pressed, .. } => !*pressed,
            InputEventData::JoypadButton { pressed, .. } => !*pressed,
            InputEventData::MouseMotion { .. } => false,
            InputEventData::MouseWheel { .. } => false,
            InputEventData::JoypadAxis { value, .. } => value.abs() <= 0.1,
        }
    }
    
    /// Get a human-readable description of this event
    pub fn get_description(&self) -> String {
        match self {
            InputEventData::KeyPressed { key_code, pressed, echo } => {
                format!("Key {:?} {} {}", key_code, 
                    if *pressed { "pressed" } else { "released" },
                    if *echo { "(echo)" } else { "" })
            }
            InputEventData::MouseButton { button, pressed, position } => {
                format!("Mouse {:?} {} at ({:.1}, {:.1})", button,
                    if *pressed { "pressed" } else { "released" },
                    position.0, position.1)
            }
            InputEventData::MouseMotion { position, relative } => {
                format!("Mouse moved to ({:.1}, {:.1}), relative ({:.1}, {:.1})",
                    position.0, position.1, relative.0, relative.1)
            }
            InputEventData::MouseWheel { delta } => {
                format!("Mouse wheel scrolled ({:.1}, {:.1})", delta.0, delta.1)
            }
            InputEventData::JoypadButton { device_id, button, pressed } => {
                format!("Gamepad {} button {} {}", device_id, button,
                    if *pressed { "pressed" } else { "released" })
            }
            InputEventData::JoypadAxis { device_id, axis, value } => {
                format!("Gamepad {} axis {} moved to {:.2}", device_id, axis, value)
            }
        }
    }
}

/// Input event builder for creating input events programmatically
pub struct InputEventBuilder;

impl InputEventBuilder {
    /// Create a key press event
    pub fn key_pressed(key_code: KeyCode) -> InputEventType {
        InputEventType::Key {
            key_code,
            pressed: true,
        }
    }
    
    /// Create a key release event
    pub fn key_released(key_code: KeyCode) -> InputEventType {
        InputEventType::Key {
            key_code,
            pressed: false,
        }
    }
    
    /// Create a mouse button press event
    pub fn mouse_button_pressed(button: MouseButton) -> InputEventType {
        InputEventType::MouseButton {
            button,
            pressed: true,
        }
    }
    
    /// Create a mouse button release event
    pub fn mouse_button_released(button: MouseButton) -> InputEventType {
        InputEventType::MouseButton {
            button,
            pressed: false,
        }
    }
    
    /// Create a mouse motion event
    pub fn mouse_motion(relative: (f32, f32)) -> InputEventType {
        InputEventType::MouseMotion { relative }
    }
    
    /// Create a gamepad button press event
    pub fn joypad_button_pressed(device_id: u32, button: u32) -> InputEventType {
        InputEventType::Joypad {
            device_id,
            button,
            pressed: true,
        }
    }
    
    /// Create a gamepad button release event
    pub fn joypad_button_released(device_id: u32, button: u32) -> InputEventType {
        InputEventType::Joypad {
            device_id,
            button,
            pressed: false,
        }
    }
}

/// Input event filter for processing input events
#[derive(Debug, Clone)]
pub struct InputEventFilter {
    /// Only process events from specific devices
    pub device_filter: Option<Vec<u32>>,
    /// Minimum strength threshold
    pub strength_threshold: f32,
    /// Only process pressed events
    pub pressed_only: bool,
    /// Only process released events
    pub released_only: bool,
}

impl InputEventFilter {
    /// Create a new input event filter
    pub fn new() -> Self {
        Self {
            device_filter: None,
            strength_threshold: 0.0,
            pressed_only: false,
            released_only: false,
        }
    }
    
    /// Set device filter
    pub fn with_devices(mut self, devices: Vec<u32>) -> Self {
        self.device_filter = Some(devices);
        self
    }
    
    /// Set strength threshold
    pub fn with_strength_threshold(mut self, threshold: f32) -> Self {
        self.strength_threshold = threshold;
        self
    }
    
    /// Only process pressed events
    pub fn pressed_only(mut self) -> Self {
        self.pressed_only = true;
        self.released_only = false;
        self
    }
    
    /// Only process released events
    pub fn released_only(mut self) -> Self {
        self.released_only = true;
        self.pressed_only = false;
        self
    }
    
    /// Check if an event passes this filter
    pub fn passes(&self, event: &InputEventData) -> bool {
        // Check device filter
        if let Some(ref devices) = self.device_filter {
            match event {
                InputEventData::JoypadButton { device_id, .. } |
                InputEventData::JoypadAxis { device_id, .. } => {
                    if !devices.contains(device_id) {
                        return false;
                    }
                }
                _ => {} // Non-gamepad events pass device filter
            }
        }
        
        // Check strength threshold
        if event.get_strength() < self.strength_threshold {
            return false;
        }
        
        // Check pressed/released filter
        if self.pressed_only && !event.is_pressed() {
            return false;
        }
        
        if self.released_only && !event.is_released() {
            return false;
        }
        
        true
    }
}

impl Default for InputEventFilter {
    fn default() -> Self {
        Self::new()
    }
}
