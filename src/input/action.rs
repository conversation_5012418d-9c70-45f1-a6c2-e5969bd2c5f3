use super::*;

/// Action state for tracking input action states
#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Eq)]
pub enum ActionState {
    /// Action is not currently active
    Released,
    /// Action was just pressed this frame
    JustPressed,
    /// Action is currently active/held
    Pressed,
    /// Action was just released this frame
    JustReleased,
}

impl ActionState {
    /// Check if the action is currently active (pressed or just pressed)
    pub fn is_active(&self) -> bool {
        matches!(self, ActionState::Pressed | ActionState::JustPressed)
    }
    
    /// Check if the action was just activated this frame
    pub fn is_just_activated(&self) -> bool {
        matches!(self, ActionState::JustPressed)
    }
    
    /// Check if the action was just deactivated this frame
    pub fn is_just_deactivated(&self) -> bool {
        matches!(self, ActionState::JustReleased)
    }
    
    /// Check if the action is inactive
    pub fn is_inactive(&self) -> bool {
        matches!(self, ActionState::Released | ActionState::JustReleased)
    }
}

impl Default for ActionState {
    fn default() -> Self {
        ActionState::Released
    }
}

/// Action definition - maps input events to a named action
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct Action {
    /// Input events that trigger this action
    pub events: Vec<InputEventType>,
    /// Deadzone for analog inputs (0.0 to 1.0)
    pub deadzone: f32,
}

impl Action {
    /// Create a new action
    pub fn new(deadzone: f32) -> Self {
        Self {
            events: Vec::new(),
            deadzone: deadzone.clamp(0.0, 1.0),
        }
    }
    
    /// Add an input event to this action
    pub fn add_event(&mut self, event: InputEventType) {
        if !self.events.contains(&event) {
            self.events.push(event);
        }
    }
    
    /// Remove an input event from this action
    pub fn remove_event(&mut self, event: &InputEventType) {
        self.events.retain(|e| e != event);
    }
    
    /// Check if this action contains a specific event
    pub fn has_event(&self, event: &InputEventType) -> bool {
        self.events.contains(event)
    }
    
    /// Clear all events from this action
    pub fn clear_events(&mut self) {
        self.events.clear();
    }
    
    /// Get the number of events in this action
    pub fn event_count(&self) -> usize {
        self.events.len()
    }
    
    /// Check if any of the action's events match the given input event data
    pub fn matches_event(&self, event_data: &InputEventData) -> bool {
        self.events.iter().any(|event| event_data.matches(event))
    }
    
    /// Get the strength of this action based on input event data
    pub fn get_strength(&self, event_data: &InputEventData) -> f32 {
        if self.matches_event(event_data) {
            let strength = event_data.get_strength();
            if strength >= self.deadzone {
                strength
            } else {
                0.0
            }
        } else {
            0.0
        }
    }
    
    /// Set the deadzone for this action
    pub fn set_deadzone(&mut self, deadzone: f32) {
        self.deadzone = deadzone.clamp(0.0, 1.0);
    }
    
    /// Get a human-readable description of this action
    pub fn get_description(&self) -> String {
        if self.events.is_empty() {
            "No events assigned".to_string()
        } else {
            format!("{} event(s), deadzone: {:.2}", self.events.len(), self.deadzone)
        }
    }
}

/// Action manager for handling multiple actions
#[derive(Debug, Default)]
pub struct ActionManager {
    actions: HashMap<String, Action>,
    action_states: HashMap<String, ActionState>,
    action_strengths: HashMap<String, f32>,
}

impl ActionManager {
    /// Create a new action manager
    pub fn new() -> Self {
        Self {
            actions: HashMap::new(),
            action_states: HashMap::new(),
            action_strengths: HashMap::new(),
        }
    }
    
    /// Add an action
    pub fn add_action(&mut self, name: String, action: Action) {
        self.actions.insert(name.clone(), action);
        self.action_states.insert(name.clone(), ActionState::Released);
        self.action_strengths.insert(name, 0.0);
    }
    
    /// Remove an action
    pub fn remove_action(&mut self, name: &str) {
        self.actions.remove(name);
        self.action_states.remove(name);
        self.action_strengths.remove(name);
    }
    
    /// Get an action
    pub fn get_action(&self, name: &str) -> Option<&Action> {
        self.actions.get(name)
    }
    
    /// Get an action mutably
    pub fn get_action_mut(&mut self, name: &str) -> Option<&mut Action> {
        self.actions.get_mut(name)
    }
    
    /// Get action state
    pub fn get_action_state(&self, name: &str) -> ActionState {
        self.action_states.get(name).copied().unwrap_or(ActionState::Released)
    }
    
    /// Get action strength
    pub fn get_action_strength(&self, name: &str) -> f32 {
        self.action_strengths.get(name).copied().unwrap_or(0.0)
    }
    
    /// Update action states based on input events
    pub fn update(&mut self, input_events: &[InputEventData]) {
        // Reset all action states for this frame
        for (name, state) in &mut self.action_states {
            *state = match *state {
                ActionState::JustPressed => ActionState::Pressed,
                ActionState::JustReleased => ActionState::Released,
                other => other,
            };
        }
        
        // Process input events
        for event in input_events {
            for (action_name, action) in &self.actions {
                if action.matches_event(event) {
                    let strength = action.get_strength(event);
                    self.action_strengths.insert(action_name.clone(), strength);
                    
                    if event.is_pressed() && strength > 0.0 {
                        let current_state = self.get_action_state(action_name);
                        if !current_state.is_active() {
                            self.action_states.insert(action_name.clone(), ActionState::JustPressed);
                        }
                    } else if event.is_released() {
                        let current_state = self.get_action_state(action_name);
                        if current_state.is_active() {
                            self.action_states.insert(action_name.clone(), ActionState::JustReleased);
                        }
                    }
                }
            }
        }
    }
    
    /// Check if an action is currently pressed
    pub fn is_action_pressed(&self, name: &str) -> bool {
        self.get_action_state(name).is_active()
    }
    
    /// Check if an action was just pressed this frame
    pub fn is_action_just_pressed(&self, name: &str) -> bool {
        self.get_action_state(name).is_just_activated()
    }
    
    /// Check if an action was just released this frame
    pub fn is_action_just_released(&self, name: &str) -> bool {
        self.get_action_state(name).is_just_deactivated()
    }
    
    /// Get all action names
    pub fn get_action_names(&self) -> Vec<String> {
        self.actions.keys().cloned().collect()
    }
    
    /// Clear all actions
    pub fn clear(&mut self) {
        self.actions.clear();
        self.action_states.clear();
        self.action_strengths.clear();
    }
    
    /// Get actions that are currently active
    pub fn get_active_actions(&self) -> Vec<String> {
        self.action_states
            .iter()
            .filter_map(|(name, state)| {
                if state.is_active() {
                    Some(name.clone())
                } else {
                    None
                }
            })
            .collect()
    }
    
    /// Get actions that were just activated this frame
    pub fn get_just_activated_actions(&self) -> Vec<String> {
        self.action_states
            .iter()
            .filter_map(|(name, state)| {
                if state.is_just_activated() {
                    Some(name.clone())
                } else {
                    None
                }
            })
            .collect()
    }
    
    /// Get actions that were just deactivated this frame
    pub fn get_just_deactivated_actions(&self) -> Vec<String> {
        self.action_states
            .iter()
            .filter_map(|(name, state)| {
                if state.is_just_deactivated() {
                    Some(name.clone())
                } else {
                    None
                }
            })
            .collect()
    }
}

/// Action binding for mapping specific input combinations to actions
#[derive(Debug, Clone)]
pub struct ActionBinding {
    /// The action name
    pub action: String,
    /// Required input events (all must be active)
    pub required_events: Vec<InputEventType>,
    /// Optional modifier events
    pub modifier_events: Vec<InputEventType>,
    /// Minimum strength required
    pub min_strength: f32,
}

impl ActionBinding {
    /// Create a new action binding
    pub fn new(action: String) -> Self {
        Self {
            action,
            required_events: Vec::new(),
            modifier_events: Vec::new(),
            min_strength: 0.0,
        }
    }
    
    /// Add a required input event
    pub fn with_required_event(mut self, event: InputEventType) -> Self {
        self.required_events.push(event);
        self
    }
    
    /// Add a modifier event
    pub fn with_modifier_event(mut self, event: InputEventType) -> Self {
        self.modifier_events.push(event);
        self
    }
    
    /// Set minimum strength
    pub fn with_min_strength(mut self, strength: f32) -> Self {
        self.min_strength = strength;
        self
    }
    
    /// Check if this binding matches the current input state
    pub fn matches(&self, input_events: &[InputEventData]) -> bool {
        // Check if all required events are present
        for required in &self.required_events {
            if !input_events.iter().any(|event| event.matches(required)) {
                return false;
            }
        }
        
        // Check if any modifier events are present (if specified)
        if !self.modifier_events.is_empty() {
            let has_modifier = self.modifier_events.iter().any(|modifier| {
                input_events.iter().any(|event| event.matches(modifier))
            });
            if !has_modifier {
                return false;
            }
        }
        
        // Check minimum strength
        let total_strength: f32 = input_events
            .iter()
            .map(|event| event.get_strength())
            .sum();
        
        total_strength >= self.min_strength
    }
}
