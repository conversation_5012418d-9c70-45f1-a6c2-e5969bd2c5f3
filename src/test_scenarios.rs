use crate::physics::PhysicsWorld;
use nalgebra::Vector2;

/// Available test scenarios
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq)]
pub enum TestScenario {
    Empty,
    BasicDrop,
    Tower,
    Pyramid,
    Avalanche,
    StressTest,
    CollisionTest,
    StabilityTest,
    ZeroGravity,
    HighGravity,
}

impl TestScenario {
    /// Get all available test scenarios
    pub fn all() -> Vec<TestScenario> {
        vec![
            TestScenario::Empty,
            TestScenario::BasicDrop,
            TestScenario::Tower,
            TestScenario::Pyramid,
            TestScenario::Avalanche,
            TestScenario::StressTest,
            TestScenario::CollisionTest,
            TestScenario::StabilityTest,
            TestScenario::ZeroGravity,
            TestScenario::HighGravity,
        ]
    }

    /// Get the display name for the scenario
    pub fn name(&self) -> &'static str {
        match self {
            TestScenario::Empty => "Empty Scene",
            TestScenario::BasicDrop => "Basic Drop Test",
            TestScenario::Tower => "Tower Stack",
            TestScenario::Pyramid => "Pyramid",
            TestScenario::Avalanche => "Avalanche",
            TestScenario::StressTest => "Stress Test (100+ objects)",
            TestScenario::CollisionTest => "Collision Test",
            TestScenario::StabilityTest => "Stability Test",
            TestScenario::ZeroGravity => "Zero Gravity Test",
            TestScenario::HighGravity => "High Gravity Test",
        }
    }

    /// Get the description for the scenario
    pub fn description(&self) -> &'static str {
        match self {
            TestScenario::Empty => "Clear all objects for a clean start",
            TestScenario::BasicDrop => "Drop a few balls from different heights",
            TestScenario::Tower => "Stack boxes in a tall tower to test stability",
            TestScenario::Pyramid => "Create a pyramid structure with boxes",
            TestScenario::Avalanche => "Create an unstable pile that will collapse",
            TestScenario::StressTest => "Spawn many objects to test performance",
            TestScenario::CollisionTest => "Test collision detection with moving objects",
            TestScenario::StabilityTest => "Test physics stability with complex structures",
            TestScenario::ZeroGravity => "Test behavior in zero gravity environment",
            TestScenario::HighGravity => "Test behavior with increased gravity",
        }
    }

    /// Execute the test scenario
    pub fn execute(&self, physics_world: &mut PhysicsWorld) {
        // Clear existing dynamic objects first
        physics_world.clear_dynamic_objects();

        match self {
            TestScenario::Empty => {
                // Already cleared, nothing more to do
                physics_world.set_gravity(Vector2::new(0.0, -9.81));
            }
            TestScenario::BasicDrop => {
                physics_world.set_gravity(Vector2::new(0.0, -9.81));
                
                // Drop balls from different heights
                physics_world.create_ball(Vector2::new(-3.0, 8.0), 0.5);
                physics_world.create_ball(Vector2::new(0.0, 10.0), 0.7);
                physics_world.create_ball(Vector2::new(3.0, 12.0), 0.6);
                physics_world.create_ball(Vector2::new(-1.5, 6.0), 0.4);
                physics_world.create_ball(Vector2::new(1.5, 14.0), 0.8);
            }
            TestScenario::Tower => {
                physics_world.set_gravity(Vector2::new(0.0, -9.81));
                
                // Create a tower of boxes
                for i in 0..8 {
                    let y = 1.0 + i as f32 * 1.1;
                    physics_world.create_box(Vector2::new(0.0, y), Vector2::new(0.5, 0.5));
                }
            }
            TestScenario::Pyramid => {
                physics_world.set_gravity(Vector2::new(0.0, -9.81));
                
                // Create a pyramid structure
                let base_width = 5;
                for layer in 0..base_width {
                    let boxes_in_layer = base_width - layer;
                    let y = 1.0 + layer as f32 * 1.1;
                    
                    for i in 0..boxes_in_layer {
                        let x_offset = (i as f32 - (boxes_in_layer - 1) as f32 / 2.0) * 1.1;
                        physics_world.create_box(Vector2::new(x_offset, y), Vector2::new(0.5, 0.5));
                    }
                }
            }
            TestScenario::Avalanche => {
                physics_world.set_gravity(Vector2::new(0.0, -9.81));
                
                // Create an unstable pile
                for i in 0..20 {
                    let x = (i as f32 % 5.0 - 2.0) * 0.8 + (i as f32 * 0.1).sin() * 0.3;
                    let y = 5.0 + (i / 5) as f32 * 0.9;
                    
                    if i % 3 == 0 {
                        physics_world.create_ball(Vector2::new(x, y), 0.4);
                    } else {
                        physics_world.create_box(Vector2::new(x, y), Vector2::new(0.4, 0.4));
                    }
                }
                
                // Add a trigger ball
                physics_world.create_ball(Vector2::new(-5.0, 10.0), 1.0);
            }
            TestScenario::StressTest => {
                physics_world.set_gravity(Vector2::new(0.0, -9.81));
                
                // Create many objects for performance testing
                for i in 0..120 {
                    let x = (i % 12) as f32 * 1.5 - 8.0;
                    let y = 5.0 + (i / 12) as f32 * 1.2;
                    
                    if i % 2 == 0 {
                        physics_world.create_ball(Vector2::new(x, y), 0.3);
                    } else {
                        physics_world.create_box(Vector2::new(x, y), Vector2::new(0.3, 0.3));
                    }
                }
            }
            TestScenario::CollisionTest => {
                physics_world.set_gravity(Vector2::new(0.0, -9.81));
                
                // Create objects that will collide
                physics_world.create_ball(Vector2::new(-8.0, 8.0), 0.8);
                physics_world.create_ball(Vector2::new(8.0, 8.0), 0.8);
                
                // Apply initial velocities (this would need to be done after creation)
                // For now, just create positioned objects
                for i in 0..5 {
                    physics_world.create_ball(Vector2::new(-4.0 + i as f32 * 2.0, 6.0), 0.5);
                }
            }
            TestScenario::StabilityTest => {
                physics_world.set_gravity(Vector2::new(0.0, -9.81));
                
                // Create a complex structure to test stability
                // Base platform
                for i in 0..6 {
                    physics_world.create_box(Vector2::new(-2.5 + i as f32 * 1.0, 2.0), Vector2::new(0.5, 0.2));
                }
                
                // Vertical supports
                physics_world.create_box(Vector2::new(-2.0, 3.5), Vector2::new(0.2, 1.0));
                physics_world.create_box(Vector2::new(2.0, 3.5), Vector2::new(0.2, 1.0));
                
                // Top platform
                for i in 0..4 {
                    physics_world.create_box(Vector2::new(-1.5 + i as f32 * 1.0, 5.0), Vector2::new(0.5, 0.2));
                }
                
                // Add some balls on top
                physics_world.create_ball(Vector2::new(-1.0, 6.0), 0.4);
                physics_world.create_ball(Vector2::new(0.0, 6.0), 0.4);
                physics_world.create_ball(Vector2::new(1.0, 6.0), 0.4);
            }
            TestScenario::ZeroGravity => {
                physics_world.set_gravity(Vector2::new(0.0, 0.0));
                
                // Create floating objects
                for i in 0..15 {
                    let angle = i as f32 * 2.0 * std::f32::consts::PI / 15.0;
                    let radius = 3.0;
                    let x = angle.cos() * radius;
                    let y = 5.0 + angle.sin() * radius;
                    
                    if i % 2 == 0 {
                        physics_world.create_ball(Vector2::new(x, y), 0.4);
                    } else {
                        physics_world.create_box(Vector2::new(x, y), Vector2::new(0.4, 0.4));
                    }
                }
            }
            TestScenario::HighGravity => {
                physics_world.set_gravity(Vector2::new(0.0, -25.0));
                
                // Create objects that will fall quickly
                for i in 0..10 {
                    let x = (i as f32 - 4.5) * 1.0;
                    let y = 8.0 + (i % 3) as f32 * 2.0;
                    
                    physics_world.create_ball(Vector2::new(x, y), 0.5);
                }
                
                // Add some boxes
                for i in 0..5 {
                    let x = (i as f32 - 2.0) * 1.5;
                    physics_world.create_box(Vector2::new(x, 12.0), Vector2::new(0.5, 0.5));
                }
            }
        }
    }
}

/// Test scenario manager
pub struct TestScenarioManager {
    pub current_scenario: TestScenario,
    pub auto_run_next: bool,
    pub scenario_timer: f32,
    pub scenario_duration: f32,
}

impl TestScenarioManager {
    /// Create a new test scenario manager
    pub fn new() -> Self {
        Self {
            current_scenario: TestScenario::Empty,
            auto_run_next: false,
            scenario_timer: 0.0,
            scenario_duration: 30.0, // 30 seconds per scenario
        }
    }

    /// Update the scenario manager
    pub fn update(&mut self, dt: f32, physics_world: &mut PhysicsWorld) {
        if self.auto_run_next {
            self.scenario_timer += dt;
            
            if self.scenario_timer >= self.scenario_duration {
                self.next_scenario(physics_world);
                self.scenario_timer = 0.0;
            }
        }
    }

    /// Run a specific scenario
    pub fn run_scenario(&mut self, scenario: TestScenario, physics_world: &mut PhysicsWorld) {
        self.current_scenario = scenario;
        scenario.execute(physics_world);
        self.scenario_timer = 0.0;
    }

    /// Move to the next scenario in sequence
    pub fn next_scenario(&mut self, physics_world: &mut PhysicsWorld) {
        let scenarios = TestScenario::all();
        let current_index = scenarios.iter().position(|&s| s == self.current_scenario).unwrap_or(0);
        let next_index = (current_index + 1) % scenarios.len();
        
        self.run_scenario(scenarios[next_index], physics_world);
    }

    /// Get remaining time for current scenario
    pub fn remaining_time(&self) -> f32 {
        if self.auto_run_next {
            (self.scenario_duration - self.scenario_timer).max(0.0)
        } else {
            0.0
        }
    }
}

impl Default for TestScenarioManager {
    fn default() -> Self {
        Self::new()
    }
}
