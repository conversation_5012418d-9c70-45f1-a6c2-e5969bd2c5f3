use crate::scene_graph::*;
use nalgebra::Matrix4;
use std::collections::HashMap;
use wgpu::util::DeviceExt;

/// Uniform data for transform matrix
#[repr(C)]
#[derive(Debug, <PERSON>lone, Copy, bytemuck::Pod, bytemuck::Zeroable)]
pub struct TransformUniform {
    pub transform: [[f32; 4]; 4],
}

impl TransformUniform {
    pub fn new(transform: Matrix4<f32>) -> Self {
        Self {
            transform: transform.into(),
        }
    }
}

/// Uniform data for color
#[repr(C)]
#[derive(Debug, Clone, Copy, bytemuck::Pod, bytemuck::Zeroable)]
pub struct ColorUniform {
    pub color: [f32; 4],
}

impl ColorUniform {
    pub fn new(color: [f32; 4]) -> Self {
        Self { color }
    }
}

/// Renderer that handles scene graph rendering
pub struct SceneRenderer {
    render_pipeline: wgpu::RenderPipeline,
    bind_group_layout: wgpu::BindGroupLayout,
    
    // Buffers for rendering
    vertex_buffers: HashMap<NodeId, wgpu::Buffer>,
    index_buffers: HashMap<NodeId, wgpu::Buffer>,
    transform_buffers: HashMap<NodeId, wgpu::Buffer>,
    color_buffers: HashMap<NodeId, wgpu::Buffer>,
    bind_groups: HashMap<NodeId, wgpu::BindGroup>,
    
    // Cached render data
    render_data_cache: HashMap<NodeId, (RenderData, Transform2D)>,
}

impl SceneRenderer {
    /// Create a new scene renderer
    pub fn new(device: &wgpu::Device, surface_format: wgpu::TextureFormat) -> Self {
        // Create bind group layout
        let bind_group_layout = device.create_bind_group_layout(&wgpu::BindGroupLayoutDescriptor {
            label: Some("Scene Renderer Bind Group Layout"),
            entries: &[
                // Transform uniform
                wgpu::BindGroupLayoutEntry {
                    binding: 0,
                    visibility: wgpu::ShaderStages::VERTEX,
                    ty: wgpu::BindingType::Buffer {
                        ty: wgpu::BufferBindingType::Uniform,
                        has_dynamic_offset: false,
                        min_binding_size: None,
                    },
                    count: None,
                },
                // Color uniform
                wgpu::BindGroupLayoutEntry {
                    binding: 1,
                    visibility: wgpu::ShaderStages::FRAGMENT,
                    ty: wgpu::BindingType::Buffer {
                        ty: wgpu::BufferBindingType::Uniform,
                        has_dynamic_offset: false,
                        min_binding_size: None,
                    },
                    count: None,
                },
            ],
        });

        // Create shader module
        let shader = device.create_shader_module(wgpu::ShaderModuleDescriptor {
            label: Some("Scene Renderer Shader"),
            source: wgpu::ShaderSource::Wgsl(std::borrow::Cow::Borrowed(include_str!("shader.wgsl"))),
        });

        // Create render pipeline layout
        let render_pipeline_layout = device.create_pipeline_layout(&wgpu::PipelineLayoutDescriptor {
            label: Some("Scene Renderer Pipeline Layout"),
            bind_group_layouts: &[&bind_group_layout],
            push_constant_ranges: &[],
        });

        // Create render pipeline
        let render_pipeline = device.create_render_pipeline(&wgpu::RenderPipelineDescriptor {
            label: Some("Scene Renderer Pipeline"),
            layout: Some(&render_pipeline_layout),
            vertex: wgpu::VertexState {
                module: &shader,
                entry_point: Some("vs_main"),
                buffers: &[Vertex::desc()],
                compilation_options: wgpu::PipelineCompilationOptions::default(),
            },
            fragment: Some(wgpu::FragmentState {
                module: &shader,
                entry_point: Some("fs_main"),
                targets: &[Some(wgpu::ColorTargetState {
                    format: surface_format,
                    blend: Some(wgpu::BlendState::ALPHA_BLENDING),
                    write_mask: wgpu::ColorWrites::ALL,
                })],
                compilation_options: wgpu::PipelineCompilationOptions::default(),
            }),
            primitive: wgpu::PrimitiveState {
                topology: wgpu::PrimitiveTopology::TriangleList,
                strip_index_format: None,
                front_face: wgpu::FrontFace::Ccw,
                cull_mode: Some(wgpu::Face::Back),
                polygon_mode: wgpu::PolygonMode::Fill,
                unclipped_depth: false,
                conservative: false,
            },
            depth_stencil: None,
            multisample: wgpu::MultisampleState {
                count: 1,
                mask: !0,
                alpha_to_coverage_enabled: false,
            },
            multiview: None,
            cache: None,
        });

        Self {
            render_pipeline,
            bind_group_layout,
            vertex_buffers: HashMap::new(),
            index_buffers: HashMap::new(),
            transform_buffers: HashMap::new(),
            color_buffers: HashMap::new(),
            bind_groups: HashMap::new(),
            render_data_cache: HashMap::new(),
        }
    }

    /// Update render data for a node
    pub fn update_node(&mut self, device: &wgpu::Device, node_id: NodeId, render_data: RenderData, transform: Transform2D) {
        // Check if we need to update the buffers
        let needs_update = if let Some((cached_data, cached_transform)) = self.render_data_cache.get(&node_id) {
            cached_data.vertices != render_data.vertices ||
            cached_data.indices != render_data.indices ||
            cached_data.color != render_data.color ||
            cached_transform.position != transform.position ||
            cached_transform.rotation != transform.rotation ||
            cached_transform.scale != transform.scale
        } else {
            true
        };

        if needs_update {
            // Create or update vertex buffer
            let vertex_buffer = device.create_buffer_init(&wgpu::util::BufferInitDescriptor {
                label: Some(&format!("Vertex Buffer {}", node_id.0)),
                contents: bytemuck::cast_slice(&render_data.vertices),
                usage: wgpu::BufferUsages::VERTEX,
            });

            // Create or update index buffer
            let index_buffer = device.create_buffer_init(&wgpu::util::BufferInitDescriptor {
                label: Some(&format!("Index Buffer {}", node_id.0)),
                contents: bytemuck::cast_slice(&render_data.indices),
                usage: wgpu::BufferUsages::INDEX,
            });

            // Create transform uniform
            let transform_matrix = transform.to_matrix4();
            let transform_uniform = TransformUniform::new(transform_matrix);
            let transform_buffer = device.create_buffer_init(&wgpu::util::BufferInitDescriptor {
                label: Some(&format!("Transform Buffer {}", node_id.0)),
                contents: bytemuck::cast_slice(&[transform_uniform]),
                usage: wgpu::BufferUsages::UNIFORM | wgpu::BufferUsages::COPY_DST,
            });

            // Create color uniform
            let color_uniform = ColorUniform::new(render_data.color);
            let color_buffer = device.create_buffer_init(&wgpu::util::BufferInitDescriptor {
                label: Some(&format!("Color Buffer {}", node_id.0)),
                contents: bytemuck::cast_slice(&[color_uniform]),
                usage: wgpu::BufferUsages::UNIFORM | wgpu::BufferUsages::COPY_DST,
            });

            // Create bind group
            let bind_group = device.create_bind_group(&wgpu::BindGroupDescriptor {
                label: Some(&format!("Bind Group {}", node_id.0)),
                layout: &self.bind_group_layout,
                entries: &[
                    wgpu::BindGroupEntry {
                        binding: 0,
                        resource: transform_buffer.as_entire_binding(),
                    },
                    wgpu::BindGroupEntry {
                        binding: 1,
                        resource: color_buffer.as_entire_binding(),
                    },
                ],
            });

            // Store buffers and bind group
            self.vertex_buffers.insert(node_id, vertex_buffer);
            self.index_buffers.insert(node_id, index_buffer);
            self.transform_buffers.insert(node_id, transform_buffer);
            self.color_buffers.insert(node_id, color_buffer);
            self.bind_groups.insert(node_id, bind_group);

            // Cache the render data
            self.render_data_cache.insert(node_id, (render_data, transform));
        }
    }

    /// Remove a node from the renderer
    pub fn remove_node(&mut self, node_id: NodeId) {
        self.vertex_buffers.remove(&node_id);
        self.index_buffers.remove(&node_id);
        self.transform_buffers.remove(&node_id);
        self.color_buffers.remove(&node_id);
        self.bind_groups.remove(&node_id);
        self.render_data_cache.remove(&node_id);
    }

    /// Render a scene with CanvasItem support
    pub fn render_scene(&self, scene: &Scene, render_pass: &mut wgpu::RenderPass) {
        render_pass.set_pipeline(&self.render_pipeline);

        // Collect and render all renderable nodes
        let renderable_nodes = scene.collect_renderable_nodes();

        // Sort nodes by z_index for proper rendering order
        let mut sorted_nodes: Vec<_> = renderable_nodes.into_iter().collect();
        sorted_nodes.sort_by(|a, b| {
            // In a real implementation, we'd check if nodes implement CanvasItem
            // and sort by their z_index. For now, we'll use node ID as a simple ordering
            a.0.0.cmp(&b.0.0)
        });

        for (node_id, _node) in sorted_nodes {
            if let (Some(vertex_buffer), Some(index_buffer), Some(bind_group)) = (
                self.vertex_buffers.get(&node_id),
                self.index_buffers.get(&node_id),
                self.bind_groups.get(&node_id),
            ) {
                if let Some((render_data, _)) = self.render_data_cache.get(&node_id) {
                    // In a real implementation, we'd check visibility and apply modulation here
                    render_pass.set_bind_group(0, bind_group, &[]);
                    render_pass.set_vertex_buffer(0, vertex_buffer.slice(..));
                    render_pass.set_index_buffer(index_buffer.slice(..), wgpu::IndexFormat::Uint16);
                    render_pass.draw_indexed(0..render_data.indices.len() as u32, 0, 0..1);
                }
            }
        }
    }

    /// Update all nodes in a scene with CanvasItem support
    pub fn update_scene(&mut self, device: &wgpu::Device, scene: &Scene) {
        let renderable_nodes = scene.collect_renderable_nodes();

        // Sort nodes by z_index for proper rendering order
        let mut sorted_nodes: Vec<_> = renderable_nodes.into_iter().collect();
        sorted_nodes.sort_by(|a, b| {
            // In a real implementation, we'd check if nodes implement CanvasItem
            // and sort by their z_index. For now, we'll use node ID as a simple ordering
            a.0.0.cmp(&b.0.0)
        });

        for (node_id, _node) in sorted_nodes {
            // Create default triangle render data for now
            // In a real implementation, we'd check if the node implements Renderable
            // and get the actual render data from it
            let render_data = RenderData {
                vertices: vec![
                    Vertex { position: [0.0, 0.1] },
                    Vertex { position: [-0.1, -0.1] },
                    Vertex { position: [0.1, -0.1] },
                ],
                indices: vec![0, 1, 2],
                color: [0.3, 0.6, 1.0, 1.0],
            };

            // Use default transform for now
            let transform = Transform2D::default();

            self.update_node(device, node_id, render_data, transform);
        }
    }
}
