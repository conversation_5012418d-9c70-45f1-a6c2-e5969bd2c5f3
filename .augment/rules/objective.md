---
type: "manual"
description: "Core ruleset - Understanding and performing objectives correctly"
---
Whenever you are presented with an objective, your first task is to understand the objective in a way, that can be fully completed, with all of the steps and sub-steps involved.
Then, understand the objective in a way, where you can identify what programming language is it associated to.
After understanding the objective fully, plan out your objective into steps and sub-steps.

Your first step in all objectives is to fetch websites with the appropriate information on the internet and utilize that knowledge from those fetched websites. This includes libraries that you'll use for the objective. You must use their latest versions according to the fetched websites' information.
Then, look at your objective and see if it aligns with the fetched websites' information. If it doesn't, then you may re-approach understanding the objective. If the objective cannot be altered that would yield the same result, then end the task saying to the user that the objective cannot be completed, with the appropriate reasons as in why not.
However if the fetched informations align with the objective, then you may start performing the tasks for that objective.
Your requirements for performing the objective will always be "Understanding the objective", "Understanding what programming language to use", "Having fetched website information from the internet", "Having the fetched websites' information align with the objective" and "A plan for completing the objective as steps and sub-steps".