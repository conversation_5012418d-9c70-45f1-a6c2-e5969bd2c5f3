---
type: "manual"
---

When it comes to creating, editing and removing Rust files (with the ".rs" format), certain rules must be followed to provide perfect results.

Before executing an objective's plan:
- Make sure the workspace can be built with "cargo build". (In case of a newly created workspace or a workspace with missing "Cargo.lock" file, 

When creating or editing Rust files:
- Make sure the file is attached to our workspace, as in attach the file into the "lib.rs" file inside the "src" folder and into the "mod.rs" file inside the folder that the Rust file lies in. (This way, diagnostics can be applied to the Rust file, so you can identify and fix errors as intended.)
- Make sure there are no linting errors and diagnostic errors.
- Make sure warnings are hidden.
- Make sure to give good documentation comments for each method where appropriate.
- Make sure to have the least amount of data being allocated as possible, including the structs and the functions. (That can be done by changing multiple data types into one type (like 2 i32 data into one u64 data))
- Make sure to use the best possible algorithms, calculations, early exists and iterations that produces the result the fastest. (This can include SIMD, Parallel programming, Multithreading, etc.)
- Make sure to include tests for each method and function you implement to identify errors. (Tests must be inside the Rust file, which can be called with "cargo test".)

When creating a temporary Rust file:
- Make sure it follows the rules for "creating or editing Rust files" as mentioned in this ruleset.
- Make sure that once the Rust file has been successfully created, you remove the other Rust file that has been associated from and rename the newly created file as the deleted Rust file. (Example: You create a "BetterVector2" Rust file and copy contexts from the "Vector2" Rust file but with some changes. Then if the "BetterVector2" Rust file has been successfully created, remove the "Vector2" Rust file and rename "BetterVector2" Rust file into "Vector2".)

When removing Rust files:
- Make sure the file is de-attached from our workspace, as in remove the contexts of this file from any "lib.rs" file inside the "src" folder, from any "mod.rs" file inside the folder that the Rust file lied in, or at any files which required the Rust file's contexts. (Essentially cleaning up after removing the Rust file.)

When finished creating, editing or removing Rust files:
- Make sure to hide warnings.
- Make sure to fix appearing problems.
- Make sure to look for clear optimizations and use them where applicable.