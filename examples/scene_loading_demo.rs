use verturion::scene_graph::*;
use verturion::*;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("Scene Loading/Saving Demo");
    
    // Create a simple scene
    let mut scene = Scene::new("DemoScene".to_string());
    
    // Add some nodes
    let root = BaseNode::new("Root".to_string());
    let root_id = scene.add_node(Box::new(root));
    scene.set_root(root_id);
    
    let sprite = Sprite2D::new("Player".to_string());
    let sprite_id = scene.add_node(Box::new(sprite));
    scene.set_parent(sprite_id, Some(root_id));
    
    let ui_root = Control::new("UI".to_string());
    let ui_id = scene.add_node(Box::new(ui_root));
    scene.set_parent(ui_id, Some(root_id));
    
    let label = Label::new("ScoreLabel".to_string(), "Score: 0".to_string());
    let label_id = scene.add_node(Box::new(label));
    scene.set_parent(label_id, Some(ui_id));
    
    println!("Created scene with {} nodes", scene.get_nodes().len());
    
    // Save the scene to a file
    let scene_loader = SceneLoader::new();
    scene_loader.save_scene(&scene, "demo_scene.json")?;
    println!("Scene saved to demo_scene.json");
    
    // Load the scene back
    let mut loader = SceneLoader::new();
    let loaded_scene = loader.load_scene("demo_scene.json")?;
    println!("Scene loaded from demo_scene.json");
    println!("Loaded scene has {} nodes", loaded_scene.get_nodes().len());
    
    // Create a PackedScene
    let packed_scene = PackedScene::from_scene(&scene, "demo_scene.json".to_string())?;
    println!("Created PackedScene");
    
    // Instantiate the PackedScene
    let instantiated_scene = packed_scene.instantiate()?;
    println!("Instantiated scene from PackedScene");
    println!("Instantiated scene has {} nodes", instantiated_scene.get_nodes().len());
    
    // Demonstrate scene manager
    let mut scene_manager = PackedSceneManager::new();
    scene_manager.change_scene_to_packed(&packed_scene)?;
    println!("Changed to packed scene using scene manager");
    
    if scene_manager.has_current_scene() {
        println!("Scene manager has a current scene");
    }
    
    // Save the packed scene
    packed_scene.save("packed_demo_scene.json")?;
    println!("PackedScene saved to packed_demo_scene.json");
    
    // Load a packed scene from file
    let loaded_packed_scene = PackedScene::load("packed_demo_scene.json")?;
    println!("PackedScene loaded from file");
    
    // Get scene metadata
    if let Some(root_type) = loaded_packed_scene.get_root_node_type() {
        println!("Root node type: {}", root_type);
    }
    
    let node_types = loaded_packed_scene.get_node_types();
    println!("Node types in scene: {:?}", node_types);
    
    println!("Scene loading/saving demo completed successfully!");
    
    Ok(())
}
